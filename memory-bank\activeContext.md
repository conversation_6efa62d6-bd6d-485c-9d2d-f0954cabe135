# Active Context

## Current Focus
We have completed the implementation of global query filters for tenant isolation, including successful database migration and schema updates. The system now enforces tenant isolation at the database level through Entity Framework Core query filters. We have also completed the implementation of the Assets Management feature, including machine key-based authentication for bot agents. This feature allows for secure storage and retrieval of assets (strings and secrets) with tenant isolation and granular access control for bot agents.

Our architecture has been clarified to be a centralized orchestration platform with distributed execution, where the central components (server) are hosted by OpenAutomate, while the execution agents (clients) are deployed and hosted by customers on their own infrastructure. Customers control how many agents they deploy based on their needs.

**UPDATED: Enhanced Bot Template with Agent SDK Integration**
We have significantly improved the Python bot template (`openautomate-bot-template`) and updated it to use the OpenAutomate Agent SDK for local communication. The template now includes:

- **Agent-Based Communication**: Uses `openautomateagent.py` SDK for local API communication with the Bot Agent service
- **Simplified Architecture**: No need for machine keys or server URLs - all communication is local
- **Asset Management Integration**: Direct access to secure assets through the Bot Agent
- **Real-time Status Updates**: Live status reporting through the agent
- **Modular Architecture**: Clean separation between core functionality and business logic through the `BaseBot` abstract class
- **Complete Utility Suite**: Specialized utility modules covering configuration, logging, environment management, file operations, and AI capabilities
- **Developer-Friendly Design**: Simple inheritance model where developers only need to implement the `execute()` method
- **Comprehensive Documentation**: Updated README with agent-based examples and usage patterns

**FIXED: Tenant Context Scoping Issue**
We have resolved a critical issue with the OData package listing where packages would appear empty after upload but work after application restart. The problem was caused by incorrect dependency injection scoping:

- **Root Cause**: `ITenantContext` was registered as Singleton instead of Scoped, causing tenant context to be shared across all requests
- **Symptoms**: Upload worked (set tenant context), but listing returned empty until app restart (singleton retained stale state)
- **Solution**: Changed registration from `AddSingleton<ITenantContext, TenantContext>()` to `AddScoped<ITenantContext, TenantContext>()`
- **Additional Fix**: Modified `TenantContext.ResolveTenantFromSlugAsync()` to use current scoped `IUnitOfWork` instead of creating new scope
- **Impact**: Proper tenant isolation now works correctly for OData endpoints and all multi-tenant operations

We are now focusing on completing the bot agent management system and improving frontend components for asset management and monitoring.

### Completed Recently
- **Fixed Tenant Context Scoping Issue**: Resolved OData package listing problem
  - Changed `ITenantContext` registration from Singleton to Scoped for proper multi-tenant isolation
  - Modified `TenantContext.ResolveTenantFromSlugAsync()` to use existing scoped `IUnitOfWork`
  - Used `GetAllIgnoringFiltersAsync()` to bypass tenant filtering when resolving tenant by slug
  - Ensured consistent tenant context across all request-scoped services
  - Verified that global query filters now work correctly with proper scoping
- **Updated Python Bot Template for Agent Communication**: Refactored the template to use local agent communication
  - Replaced `OpenAutomateAPIClient` with `openautomateagent.py` SDK
  - Updated `BaseBot` class to use agent client for asset retrieval and status updates
  - Added convenient methods: `get_asset()`, `get_all_asset_keys()`, `update_status()`, `log_to_agent()`
  - Updated configuration to use agent settings (host/port) instead of platform settings
  - Modified examples to demonstrate agent integration and asset management
  - Created new `asset_demo.py` example showing comprehensive agent features
  - Updated README to reflect agent-based architecture and communication patterns
  - Removed dependency on platform API client for direct communication
- **Enhanced Python Bot Template**: Complete redesign of the `openautomate-bot-template`
  - Created `BaseBot` abstract class with standardized lifecycle management
  - Implemented comprehensive utility modules:
    - `ConfigManager`: INI/JSON configuration with environment variable overrides
    - `LoggerSetup`: Structured logging with file rotation and console output
    - `EnvironmentManager`: Process management and system resource monitoring  
    - `FileManager`: File operations, directory management, and automated cleanup
    - `openautomateagent.py`: SDK for local Bot Agent communication
    - `OpenAIClient`: AI-powered automation features for text analysis and generation
  - Added complete example implementations including web scraper bot and asset demo
  - Created comprehensive documentation and usage guidelines
  - Updated configuration structure to support agent communication
  - Added proper dependency management with `requirements.txt`
- **Fixed Organization Unit Creation Bug**: Resolved the issue where creating an organization unit would fail with "OWNER authority not found" error
  - Modified `CreateDefaultAuthoritiesAsync` to return the created OWNER authority instead of querying for it
  - Updated `AssignOwnerAuthorityToUserAsync` to accept the OWNER authority as a parameter
  - This eliminates the transaction/context issue that was preventing the method from finding the newly created authority
- **Fixed Organization Unit Listing Bug**: Resolved the issue where users couldn't see organization units they belonged to
  - Added `GetAllIgnoringFiltersAsync` method to the repository interface to bypass tenant filtering
  - Implemented the method in the Repository class using `IgnoreQueryFilters()`
  - Updated `GetUserOrganizationUnitsAsync` to use the new method for cross-tenant discovery
  - **Architectural Improvement**: Avoided direct DbContext injection in services, maintaining proper separation of concerns
  - **Repository Pattern Enhancement**: Improved the repository pattern to support filter bypassing
  - Added nullable reference type annotations to fix compiler warnings
  - Enhanced repository interface with proper documentation for cross-tenant operations
  - Maintained clean architecture principles while solving the tenant filtering challenge
- Added global query filters for all tenant-aware entities in ApplicationDbContext
- Updated entity configurations to properly set up relationships with OrganizationUnit
- Configured indexes on OrganizationUnitId fields for improved query performance
- Added direct filtering for primary entities (BotAgent, AutomationPackage)
- Implemented hierarchical filtering for child entities (PackageVersion, Schedule, Execution)
- Successfully created and applied AddTenantQueryFilters migration
- Verified database schema changes through migration history
- Resolved PowerShell command execution issues using absolute paths
- Created comprehensive design and documentation for the Assets Management feature
- Implemented Asset entity with proper tenant isolation via OrganizationUnitId
- Added BotAgent entity with MachineKey property for secure authentication
- Created AssetBotAgent entity to manage many-to-many relationships between assets and bot agents
- Transitioned from machine name to machine key for bot agent authentication
- Extended IAssetService and added IBotAgentService with methods for managing relationships
- Implemented API controllers for assets and bot agent management
- Added database configurations with proper indexing for performance
- Enhanced security with cryptographically secure machine key generation
- Added authorization checks for asset retrieval based on bot agent permissions
- Created SignalR hub for real-time communication with bot agents
- Implemented bot status update methods via the hub
- Designed the Bot Agent architecture with Windows Service, WPF UI, and local API server components
- Created detailed documentation of the Bot Agent implementation in BotAgent.md
- Implemented the machine key authentication system for Bot Agents

### Next Steps
- **Bot Template Integration**: Complete integration of the enhanced Python bot template with the C# Bot Agent Executor
  - Update the Bot Agent Executor to support the new template structure and agent SDK
  - Ensure proper virtual environment handling for the template dependencies
  - Test the complete workflow from platform command to template execution via agent
  - Verify asset retrieval and status updates work correctly through the local API
- Complete the bot agent management user interface
- Implement heartbeat monitoring for bot agents
- Add command execution tracking through SignalR
- Add frontend components for asset management
- Create UI for managing asset-bot agent relationships
- Implement audit logging for asset access
- Create monitoring dashboard for bot agent activities
- Add encryption for secret-type assets
- Implement automated key rotation functionality
- Develop tenant management API for creating and managing organization units
- Implement UI components for organization management
- Create integration tests to verify tenant isolation is working correctly
- Ensure controllers properly respect tenant isolation through middleware
- Implement validation in service layer to double-check tenant access
- Add unit tests for global query filter functionality
- Add UI components for managing asset-bot agent relationships
- Develop the Python SDK for Bot Agent automation scripts
- Complete the WPF UI application for Bot Agent configuration
- Implement the local API server for Bot Agent communication
- Implement secure storage for sensitive information in the Bot Agent

### Active Decisions
- **Bot Template Communication Architecture**: The Python bot template now uses local agent communication:
  - `openautomateagent.py` SDK handles all communication with the Bot Agent service
  - No direct platform communication - all goes through the local Bot Agent
  - No machine keys or server URLs needed in Python scripts
  - Asset retrieval, status updates, and logging all handled via local API
  - Simplified configuration with just host/port for agent connection
- **Bot Template Architecture**: The Python bot template follows a clear inheritance pattern where:
  - `BaseBot` provides all infrastructure (logging, config, file management, agent integration)
  - Developers only implement the `execute()` method for their business logic
  - All utilities are dependency-injected and ready to use
  - Error handling and reporting are handled automatically
  - Agent integration provides seamless access to platform features
- We're using a consistent approach for tenant isolation: direct property access where possible and navigation properties for hierarchical relationships
- OrganizationUnitId is being used consistently as the tenant identifier across all entities
- Cascade delete behavior is configured for tenant-aware entities to ensure data integrity
- Global query filters are the primary mechanism for tenant isolation, complemented by middleware validation
- Entity configurations follow a consistent pattern with foreign key definitions and indexing
- We're using machine keys instead of machine names for authentication to enhance security
- AssetBotAgent entity is used to manage the many-to-many relationship with tenant isolation
- Tenant context is consistently applied across all asset-related operations
- Machine keys are generated using cryptographically secure methods
- BotAgent authentication is handled through dedicated endpoints
- Asset values are stored as plain text in the database for now, with plans to add encryption later
- SignalR is used for real-time communication between the server and bot agents
- WebSockets provide live status updates for the frontend dashboard
- The Bot Agent follows a multi-component architecture with Windows Service, WPF UI, and local API server
- Bot Agents will be deployed on customer infrastructure while the central platform is hosted by OpenAutomate
- Local API server will be used for communication between Python scripts and the Bot Agent service
- The Bot Agent Windows Service will handle execution of automation tasks and communication with the server

## Technical Considerations
- **Bot Template Agent Communication**: The template now uses local communication only
  - All communication goes through the `openautomateagent.py` SDK
  - Default connection to localhost:8080 for the Bot Agent API
  - Graceful handling when agent is not available
  - No authentication required for local communication
  - Asset retrieval and status updates are handled seamlessly
- **Bot Template Dependencies**: The template requires standard Python packages (requests, psutil, openai, etc.)
  - All dependencies are specified in requirements.txt for easy installation
  - Optional dependencies (like BeautifulSoup for web scraping) are documented in examples
  - The template gracefully handles missing optional dependencies
- PowerShell command execution issues have been resolved by:
  - Using absolute paths instead of relative paths
  - Avoiding PowerShell operators like &&
  - Direct navigation to target directories
- Integration tests should focus on verifying:
  - Proper tenant isolation across all entity types
  - Cascade delete behavior for tenant-aware entities
  - Performance impact of global query filters
- Consider implementing additional validation in controllers as a secondary tenant isolation measure
- Machine keys are generated using RandomNumberGenerator for cryptographic security
- We're using explicit foreign key relationships for proper cascading behavior
- Database indexes are created for asset keys within tenant contexts for optimized lookups
- Asset retrieval APIs validate both asset existence and bot agent authorization
- Error handling follows a consistent pattern across all controllers
- Bot agent status updates are implemented through SignalR hub methods
- Command execution to bot agents is managed through dedicated hub methods
- Bot Agent Windows Service will be implemented as a .NET service with a local API server
- WPF UI application will communicate with the Windows Service through local API and SignalR
- Python scripts will interact with the Bot Agent through a dedicated SDK

### Frontend Design and Implementation
We're currently focused on implementing a modern, professional frontend for OpenAutomate that effectively communicates its value proposition as an open-source alternative to commercial automation platforms.

Our latest completed tasks include:
- Implemented enhanced hover effects for buttons throughout the application, adding subtle animations like scale effects, shadows, and y-axis translations
- Created a landing page inspired by UiPath's design, including:
  - Hero section highlighting open-source automation capabilities
  - Features section showcasing key differentiators (no vendor lock-in, cost effectiveness, Python-based)
  - Industry solutions section with visual navigation for different sectors
  - Value proposition section with benefits and metrics
  - Testimonials section with customer quotes reinforcing the key selling points
  - Strong CTA section for conversion
  - Comprehensive footer with navigation and company information
- Developed authentication pages including sign-in and registration flows
- Started implementation of asset management UI components
- Created initial bot agent management interface with status indicators

The UI is designed to be clean, professional, and enterprise-ready, emphasizing OpenAutomate's position as a serious alternative to commercial platforms while maintaining the accessibility and flexibility benefits of open-source.

## Recent Changes
### December 29, 2024
- **Updated Bot Template for Agent Communication**: Refactored to use local agent SDK
  - Replaced platform API client with `openautomateagent.py` SDK
  - Updated `BaseBot` class with agent communication methods
  - Modified configuration to use agent settings instead of platform settings
  - Updated all examples to demonstrate agent integration
  - Created comprehensive asset management demo
  - Updated documentation to reflect agent-based architecture
  - Removed dependency on direct platform communication

### December 29, 2024
- **Enhanced Bot Template Implementation**: Complete redesign of the Python bot template
  - Implemented `BaseBot` abstract class with comprehensive infrastructure
  - Created six specialized utility modules for different automation needs
  - Added complete configuration management with INI/JSON support and environment variable overrides
  - Implemented structured logging with file rotation and console output
  - Added environment and process management capabilities
  - Created file management utilities with automated cleanup
  - Integrated OpenAutomate platform API client for seamless communication
  - Added optional OpenAI integration for AI-powered automation features
  - Provided comprehensive documentation and examples
  - Updated project structure to be more developer-friendly
  - Added dependency management and installation instructions

### April 25, 2025
- Implemented Bot Agent management foundation:
  - Added SignalR hub for real-time communication
  - Created bot status update methods
  - Added command execution infrastructure
  - Implemented heartbeat tracking in the database
  - Added initial UI components for bot agent management
  - Created bot agent status indicators with real-time updates
  - Completed comprehensive documentation for Bot Agent architecture
  - Designed the Bot Agent Windows Service with local API server

### April 22, 2025
- Completed interface design for Asset management:
  - Created asset listing page with filtering options
  - Implemented asset creation form with validation
  - Added asset detail view with access control configuration
  - Designed asset-bot agent relationship management UI
  - Implemented initial components for the asset dashboard

### April 20, 2025
- Implemented Asset Management feature with secure bot agent authentication
- Added Asset, BotAgent, and AssetBotAgent entities to the domain model
- Updated BotAgent entity to use machine key authentication
- Created service interfaces and implementations for asset and bot agent management
- Implemented API controllers for asset management and bot agent registration
- Added database context configuration with proper indexes and relationships
- Created DTOs for asset creation, update, and bot agent registration
- Updated documentation to reflect the new asset management capabilities

### April 18, 2025
- Implemented TenantQueryFilterService for centralized tenant isolation at database level:
  - Created ITenantEntity interface to mark tenant-aware entities
  - Developed TenantEntity base class for easier implementation of tenant-aware entities
  - Implemented dynamic query filter generation using expression trees
  - Ensured all tenant-aware entities implement ITenantEntity
  - Added database indexes for OrganizationUnitId to improve query performance
- Architectural improvements for tenant isolation:
  - Controller code no longer needs to handle tenant filtering explicitly
  - Services automatically respect tenant boundaries
  - Tenant resolution happens at middleware level
  - Enhanced security by preventing cross-tenant data access
- Prepared foundation for system admin functionality:
  - Designed approach for cross-tenant operations
  - Planned implementation of tenant filter bypass mechanism for admin operations
  - Created structure for admin-specific controllers and routes
  - Documented approach for admin-only API endpoints

### April 18, 2025
- Implemented API for creating and managing Organization Units:
  - Created OrganizationUnitService with capabilities for:
    - Automatic slug generation from names
    - Validating slug uniqueness
    - Impact analysis for name changes
    - Automatic creation of default authorities (OWNER, MANAGER, DEVELOPER, USER)
  - Added REST API endpoints for Organization Unit management:
    - POST /api/organization-units - Create new organization units
    - GET /api/organization-units - Retrieve organization units
    - GET /api/organization-units/{id} - Get organization unit details
    - PUT /api/organization-units/{id} - Update organization unit
    - GET /api/organization-units/{id}/impact - Check impact of name changes
  - Implemented permission-based authorization for all endpoints
  - Secured tenant isolation using Entity Framework Core global query filters

### April 15, 2025
- Established testing strategy for multi-tenant components:
  - Created test fixture for multi-tenant database setup
  - Implemented helper utilities for simulating tenant contexts
  - Designed test plans for verifying tenant isolation
  - Planned integration tests for OrganizationUnitService
  - Set up authentication simulation for testing secured endpoints

### April 12, 2025
- Implemented global query filters for tenant isolation in ApplicationDbContext
- Updated entity configurations to establish proper relationships with OrganizationUnit
- Added OrganizationUnitId to relevant entities for tenant isolation
- Applied tenant-aware configuration to BotAgent, AutomationPackage, PackageVersion, Schedule, and Execution entities
- Added indexes for OrganizationUnitId on tenant-aware entities to improve query performance
- Created AddTenantQueryFilters migration to apply database schema changes
- Updated technical documentation to reflect the implementation of tenant isolation
- Marked "Implement Global Query Filters" task as completed in the task management system

### April 10, 2025
- Renamed entity Organization to OrganizationUnit throughout the codebase
- Renamed entity OrganizationUser to OrganizationUnitUser throughout the codebase
- Updated database context and repository implementations to reflect the entity name changes
- Updated documentation to use the new entity names consistently
- Prepared for database migration to update the database schema

### April 6, 2025
- Updated OrganizationUnit management feature to allow changing organization unit names with automatic slug updates, including impact warnings
- Updated OrganizationUnit management feature to automatically generate slugs from organization unit names
- Created technical documentation for the OrganizationUnit management feature
- Designed the API endpoints and service interfaces for organization unit management
- Planned the implementation of organization unit creation, retrieval, and user management
- Implemented Shadcn UI component infrastructure in the frontend project
- Created core UI components: Button, Card, and Input
- Configured Tailwind CSS for Shadcn UI theming with animation support
- Updated tsconfig.json to support path aliases for the components directory
- Created a sample DashboardLayout using Shadcn UI components
- Implemented a dashboard page with card components for system metrics
- Created documentation for Shadcn UI integration (README-SHADCN.md)
- Fixed animation support by implementing keyframes directly in globals.css

### April 4, 2025
- Fixed EF Core query translation error in TokenService by separating database queries from computed property checks
- Modified RefreshToken and RevokeToken methods to avoid using computed properties in LINQ queries
- Implemented AuthProvider component in frontend for centralized authentication management
- Created TenantProvider component for tenant context across the application
- Updated app layout to use the new provider architecture
- Fixed SSR compatibility issues in auth components with the 'use client' directive
- Enhanced token storage with memory-first and sessionStorage fallback strategy
- Updated root layout to include AuthProvider and TenantProvider

### April 3, 2025
- Implemented JWT authentication with refresh token support
- Created `TokenService` and `UserService` for handling authentication
- Refined token management to use direct repository access instead of navigation properties
- Created `AuthController` with endpoints for login, register, and token management
- Cleaned up redundant code, including removing unused methods in `TokenService`
- Improved error handling in token-related operations
- Implemented multi-tenant architecture with path-based tenant resolution
- Created technical design document outlining the system architecture and components
- Generated comprehensive task breakdown for project implementation
- Decided to implement refresh token authentication for improved security

## Next Steps
1. Complete the bot agent management UI with monitoring capabilities
2. Implement real-time status dashboard for bot agents
3. Add asset management UI components with relationship management
4. Create audit logging for asset access and bot agent activities
5. Add encryption for secret-type assets
6. Implement automated key rotation for enhanced security
7. Create integration tests for asset and bot agent functionality
8. Develop automation package management and scheduling features
9. Implement worker service for schedule processing

## Active Decisions
### OrganizationUnit Name Mutability
- Context: Need to determine if organization unit names can be updated after creation
- Options:
  - Allow organization unit names to be changed
  - Make organization unit names immutable after creation
  - Allow changes but with admin approval
- Status: Decision changed to allow organization unit names to be updated with corresponding slug changes. When a name change would alter the slug, the system will display clear warnings about potential impacts and require explicit confirmation from the user. This enables organization units to adapt to business changes while ensuring users understand the consequences of URL path changes.

### OrganizationUnit Slug Management
- Context: Need to determine how to handle organization unit slugs when names change
- Options:
  - Keep slugs immutable regardless of name changes
  - Update slugs automatically when names change
  - Allow manual slug updates separate from names
- Status: Decision made to automatically update slugs when organization unit names change, with required confirmation. To mitigate disruption, the system will implement temporary redirects from old slugs and maintain a history of previous slugs for backward compatibility.

### OrganizationUnit Slug Generation
- Context: Need to determine how organization unit slugs are created
- Options:
  - Use a generated GUID unrelated to the name
  - Generate a slug based on the name (e.g., "Test Organization" -> "test-organization")
  - Allow manual entry of slugs
- Status: Decision made to generate slugs based on the organization unit name, using a normalized form (lowercase, hyphens for spaces, removal of special characters). This provides human-readable URLs that reflect the organization's name while ensuring URL compatibility.

### Bot Agent Authentication
- Context: Need to determine how bot agents authenticate securely
- Options:
  - Machine name-based authentication
  - API key-based authentication
  - Certificate-based authentication
  - Machine key with cryptographic security
- Status: Decision made to use machine key-based authentication with cryptographically secure random values. This provides strong security while being simpler to implement than certificate-based authentication. Machine keys can be rotated periodically to enhance security.

### Asset Value Storage
- Context: Need to determine how asset values are stored in the database
- Options:
  - Plain text storage
  - Encrypted storage
  - External key vault integration
- Status: Initial implementation uses plain text storage for simplicity, with plans to add encryption for secret-type assets in the next phase. This allows for faster development while acknowledging that enhanced security will be needed for production deployments.

### Bot Agent Communication
- Context: Need to determine how the server communicates with bot agents
- Options:
  - REST API polling
  - WebSocket/SignalR for real-time
  - Message queue
- Status: Decision made to use SignalR for real-time communication between the server and bot agents. This enables immediate command execution and status updates without the overhead of polling, while maintaining the ability to send REST API requests for configuration and data retrieval.

### Asset-Bot Agent Authorization
- Context: Need to determine how to control which bot agents can access which assets
- Options:
  - Direct permission assignments
  - Role-based access control
  - Tag-based access control
- Status: Decision made to use a many-to-many relationship model (AssetBotAgent) to explicitly link assets with authorized bot agents. This provides granular control while maintaining simplicity and performance for lookup operations 