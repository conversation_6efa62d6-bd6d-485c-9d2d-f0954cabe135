# System Patterns

## Architecture Overview
OpenAutomate follows a centralized orchestration platform with distributed execution architecture. The system consists of a web-based control panel (frontend), backend API services, worker services for job processing, and distributed bot agents that execute automation tasks on target machines. The platform uses a client-server model where the central components (server) are hosted by OpenAutomate, while the execution agents (clients) are deployed and hosted by customers on their own infrastructure. Customers control how many agents they deploy based on their needs.

The platform is built as a multi-tenant system where each organization represents a tenant, with data isolation implemented through tenant filtering.

## System Components
### Frontend
- Next.js web application for the control panel interface
- React components for dashboard, monitoring, and configuration
- WebSocket connections for real-time updates
- Authentication and authorization system with token refresh mechanism
- Tenant-specific URL routing (/{tenant-slug}/...)

### Backend
- ASP.NET Core API for core business logic and system management
- Entity Framework Core for data persistence
- SignalR for real-time communication
- Worker services for job scheduling and processing
- Python runtime for automation script execution
- JWT authentication service with refresh token support
- Tenant resolution middleware for path-based tenant identification

### Bot Agent
- Windows Service that runs in the background
- WPF UI application for configuration and monitoring
- Local API server for communication
- SignalR connection with the central OpenAutomate server
- Python SDK for automation scripts to interact with the agent
- Machine key-based authentication with the server
- Asset and credential management capabilities
- Real-time status updates and command execution

## Design Patterns
### Used Patterns
- Repository pattern for data access
- Unit of Work pattern for transaction management
- CQRS for command/query separation
- Mediator pattern for decoupling components
- Observer pattern for event notifications
- Factory pattern for creating automation tasks
- Token-based authentication with refresh mechanism
- Multi-tenant pattern with shared database and tenant filtering
- Tenant context pattern for tenant identification and isolation

### Pattern Rationale
- Repository pattern provides abstraction over data access
- Unit of Work pattern ensures transaction consistency
- CQRS separates read and write operations for better scalability
- Mediator enables loose coupling between system components
- Observer pattern facilitates real-time status updates
- Factory pattern simplifies creation of diverse automation tasks
- Refresh token pattern enhances security while maintaining user experience
- Multi-tenant pattern allows efficient resource sharing while maintaining data isolation
- Tenant context pattern ensures proper data filtering across the application

## Multi-Tenant Architecture
```mermaid
graph TD
    U[User] -->|Accesses| URL["/{tenant-slug}/..."]
    URL -->|Resolves| TM[Tenant Middleware]
    TM -->|Identifies| TC[Tenant Context]
    TC -->|Filters| DB[(Database)]
    API -->|Uses| TC
    API -->|Queries| DB
```

## Tenant Context Scoping Pattern
**CRITICAL**: The `ITenantContext` must be registered as **Scoped**, not Singleton, to ensure proper multi-tenant isolation:

```csharp
// ✅ CORRECT - Scoped registration for proper tenant isolation
builder.Services.AddScoped<ITenantContext, TenantContext>();

// ❌ INCORRECT - Singleton causes tenant context to be shared across requests
builder.Services.AddSingleton<ITenantContext, TenantContext>();
```

**Issue Resolved**: Previously, `ITenantContext` was registered as Singleton, causing:
- Tenant context shared across all requests
- Upload operations would set tenant context (working)
- OData queries would use stale/wrong tenant context (empty results)
- Required app restart to see uploaded packages in lists

**Fix Applied**: Changed registration from Singleton to Scoped in `Program.cs`:
```csharp
// Fixed in Program.cs line 93
builder.Services.AddScoped<ITenantContext, TenantContext>();
```

This ensures each HTTP request gets its own tenant context instance, properly isolated from other requests.

## Tenant Resolution Flow
```mermaid
sequenceDiagram
    participant User
    participant API
    participant TenantMiddleware
    participant TenantContext
    participant Repository
    participant Database
    
    User->>API: Request to /{tenant-slug}/api/resource
    API->>TenantMiddleware: Process request
    TenantMiddleware->>Database: Find tenant by slug
    Database-->>TenantMiddleware: Return tenant
    TenantMiddleware->>TenantContext: Store current tenant
    TenantMiddleware->>API: Continue request processing
    API->>Repository: Query data
    Repository->>TenantContext: Get current tenant ID
    Repository->>Database: Query with tenant filter
    Database-->>Repository: Return tenant-filtered data
    Repository-->>API: Return data
    API-->>User: Response
```

**Updated Flow**: The `TenantContext.ResolveTenantFromSlugAsync()` method now:
1. Uses the current scoped `IUnitOfWork` instead of creating a new scope
2. Calls `GetAllIgnoringFiltersAsync()` to bypass tenant filtering when resolving tenant by slug
3. Ensures consistent tenant context across all request-scoped services

## Component Relationships
```mermaid
graph TD
    CP[Control Panel] --> API[ASP.NET Core API]
    API --> DB[(Database)]
    API --> WS[Worker Service]
    WS --> BA[Bot Agents]
    API --> BA
    BA --> PR[Python Runtime]
    CP -- Auth --> TS[Token Service]
    TS --> DB
    API -- Tenant --> TC[Tenant Context]
    TC --> DB
```

## Authentication Flow with Refined Token Management
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant TokenService
    participant RefreshTokenRepository
    participant Database
    
    User->>Frontend: Login with credentials
    Frontend->>API: POST /api/auth/login
    API->>TokenService: Generate tokens
    TokenService->>RefreshTokenRepository: Store refresh token
    RefreshTokenRepository->>Database: Save token with UserId
    RefreshTokenRepository-->>TokenService: Token saved
    TokenService-->>API: Return tokens
    API-->>Frontend: Return tokens
    Frontend->>User: Login successful
    
    Note over User,Frontend: When access token expires
    
    Frontend->>API: Request with expired token
    API-->>Frontend: 401 Unauthorized
    Frontend->>API: POST /api/auth/refresh
    API->>TokenService: Validate refresh token
    TokenService->>RefreshTokenRepository: Find token
    RefreshTokenRepository->>Database: Query token
    Database-->>RefreshTokenRepository: Return token
    RefreshTokenRepository-->>TokenService: Token found
    TokenService->>RefreshTokenRepository: Revoke old token
    TokenService->>RefreshTokenRepository: Add new token
    RefreshTokenRepository->>Database: Save changes
    TokenService-->>API: Return new tokens
    API-->>Frontend: Return new tokens
    Frontend->>API: Retry original request
    API-->>Frontend: Return response
```

## Token Storage Pattern
The system uses direct repository access for token management rather than navigation properties to avoid concurrency issues:

```mermaid
graph TD
    TS[Token Service] --> RTR[RefreshToken Repository]
    TS -.-> UR[User Repository]
    RTR --> DB[(Database)]
    UR --> DB
    
    subgraph "Direct Repository Pattern"
    RTR
    end
    
    subgraph "Previous Navigation Property Pattern"
    UR -.-> User
    User -.-> "RefreshTokens Collection"
    end
```

## Data Flow
1. User configures automation package through control panel
2. API processes package and stores configuration in database with tenant ID
3. Worker service picks up scheduled tasks and notifies relevant bot agents
4. Bot agents download and execute automation packages
5. Execution results flow back to API and database
6. Real-time updates sent to control panel via WebSockets

## Security Patterns
- JWT-based authentication for API access with refresh token rotation
- Direct repository access for token management to prevent concurrency issues
- String-based storage with Base64 encoding for tokens
- Role-based authorization for feature access
- Multi-tenant authorization ensuring users can only access their tenant's data
- Encrypted communication between all components
- Secure package storage and distribution
- Audit logging for all system actions
- Token revocation for compromised sessions
- Machine key-based authentication for bot agents
- Granular asset access control through AssetBotAgent relationships

## Error Handling
- Global exception handling in API
- Retry mechanisms for transient failures
- Circuit breaker pattern for external service calls
- Detailed logging for troubleshooting
- User-friendly error messages in control panel
- Authentication failure recovery with refresh tokens
- Tenant resolution error handling

## Backend Architecture
OpenAutomate backend follows a clean architecture pattern with the following layers:

1. **Core Layer**: Contains domain models, interfaces, and business logic
2. **Infrastructure Layer**: Contains implementations of interfaces defined in the core layer
3. **API Layer**: Contains controllers, middleware, and API endpoints

### Multi-Tenant Architecture
The system implements a multi-tenant pattern with the following characteristics:

- **Shared Database**: All tenants share the same database with tenant filtering
- **Tenant Resolution**: Path-based tenant identification (/tenant/resource)
- **Data Isolation**: Automatic tenant filtering at the repository level using global query filters
- **Tenant Context**: Propagation of tenant context throughout the request pipeline

### Global Query Filters Implementation
The tenant isolation is primarily enforced through Entity Framework Core's global query filters:

```mermaid
graph TD
    TM[Tenant Middleware] -->|Sets| TC[Tenant Context]
    TC -->|Provides Current Tenant to| TQF[TenantQueryFilterService]
    TQF -->|Dynamically Applies Filters to| E[ITenantEntity]
    E -->|Direct Filtering| PE[Primary Entities]
    E -->|Hierarchical Filtering| CE[Child Entities]
    
    subgraph "Primary Entity Filtering"
    PE -->|Filter| BotAgent
    PE -->|Filter| AutomationPackage
    PE -->|Filter| OrganizationUnitUser
    end
    
    subgraph "Hierarchical Entity Filtering"
    CE -->|Filter via Parent| PackageVersion
    CE -->|Filter via Parent| Schedule
    CE -->|Filter via Parent| Execution
    end
```

The filtering mechanism is implemented via the TenantQueryFilterService, which:
- Uses the ITenantEntity interface to identify tenant-aware entities
- Dynamically generates expression trees for query filtering
- Applies consistent filtering logic across all tenant-aware entities
- Supports optional filter bypassing for system admin operations

The TenantEntity base class simplifies implementation by:
- Inheriting from BaseEntity for common entity properties
- Implementing ITenantEntity with OrganizationUnitId property
- Providing a consistent navigation property to OrganizationUnit

Entity configurations ensure proper relationships and indexing:
- Foreign key relationships with the OrganizationUnit entity
- Indexed OrganizationUnitId fields for optimized query performance
- Cascade delete behavior for maintaining referential integrity

### System Admin Architecture
For system administration capabilities, a separate route structure has been designed:

```mermaid
graph TD
    A[Admin] -->|Access| AR[Admin Routes]
    AR -->|Uses| AB[Admin Bypass]
    AB -->|Disables| TF[Tenant Filtering]
    AR -->|Special Authorization| AP[Admin Permissions]
    
    subgraph "Admin Controllers - /admin/*"
    AC1[OrganizationsController]
    AC2[UsersController]
    AC3[SystemController]
    end
    
    subgraph "Regular Controllers - /api/*"
    RC1[UsersController]
    RC2[AssetsController]
    RC3[PackagesController]
    end
    
    AC1 --> AB
    AC2 --> AB
    AC3 --> AB
    
    RC1 --> TF
    RC2 --> TF
    RC3 --> TF
```

The admin architecture includes:
- Separate routing pattern (/admin/* vs /api/*)
- Special permissions system for admin access
- TenantContext extension with IgnoreTenantFilter capability
- Audit logging for all cross-tenant operations
- Comprehensive security controls to prevent unauthorized access

### Repository Pattern
Data access follows the repository pattern with the following components:

- **Generic Repository**: Base repository implementation for common CRUD operations
- **Specialized Repositories**: Extended repositories for specific entity types
- **Unit of Work**: Coordinates the work of multiple repositories in a single transaction
- **Tenant Filtering**: Automatic tenant filtering applied to all queries

### Authentication System
The authentication system follows a token-based pattern with refresh tokens:

- **JWT Authentication**: Short-lived JWTs for API access
- **Refresh Tokens**: Long-lived tokens stored in the database for obtaining new JWTs
- **Token Rotation**: New refresh tokens are issued when refreshing to prevent token reuse
- **HTTP-Only Cookies**: Refresh tokens are stored in HTTP-only cookies for enhanced security
- **EF Core Query Optimization**: Database queries are optimized to avoid using computed properties in LINQ expressions
- **Machine Key Authentication**: Bot agents use cryptographically secure machine keys for authentication

### Authorization Pattern for Assets
The system implements a relationship-based authorization pattern for assets:

```mermaid
graph TD
    Asset -->|belongs to| OU[Organization Unit]
    BA[Bot Agent] -->|belongs to| OU
    Asset -->|many-to-many| BA
    Asset -.->|via| ABR[AssetBotAgent Relationship]
    BA -.->|via| ABR
    ABR -->|belongs to| OU
    
    subgraph "Authorization Flow"
    BA -->|requests asset with| MK[Machine Key]
    MK -->|validates| Auth[Auth Service]
    Auth -->|checks| ABR
    Auth -->|if authorized| Asset
    end
```

- **Asset-Bot Relationship**: Many-to-many relationship between Assets and Bot Agents through AssetBotAgent entity
- **Tenant Isolation**: All entities (Asset, BotAgent, AssetBotAgent) are tenant-aware with OrganizationUnitId
- **Authorization Check**: Two-level verification (valid machine key + explicit relationship) before asset access
- **Secure Key Generation**: Cryptographically secure random generation for machine keys

### Error Handling
The system implements a centralized error handling pattern:

- **Global Exception Middleware**: Catches all unhandled exceptions
- **Structured Error Responses**: Standardized error format for all API responses
- **Logging**: Structured logging of errors with tenant context

## Frontend Architecture

### Component Architecture
The frontend follows a component-based architecture with the following patterns:

- **Page Components**: Next.js pages that define routes
- **Layout Components**: Shared layouts for consistent UI structure
- **Feature Components**: Reusable components for specific features
- **UI Components**: Basic UI elements for consistent styling

### Provider Architecture
The application uses context providers for state management:

- **AuthProvider**: Manages authentication state across the application
- **TenantProvider**: Manages tenant context across the application
- **Provider Composition**: Providers are nested to create a hierarchy of context

### Authentication Patterns
Frontend authentication follows these patterns:

- **localStorage Access Tokens**: Access tokens are stored in localStorage for persistence
- **HTTP-only Cookies**: Refresh tokens are stored in HTTP-only cookies
- **Auto-Refresh**: Tokens are automatically refreshed before expiration
- **SSR Compatibility**: Authentication state is handled properly for server-side rendering

### Server-Side Rendering Approach
The application follows a hybrid rendering strategy:

- **Server Components**: Used for data-heavy components that don't need client interactivity
- **Client Components**: Used for interactive components with the 'use client' directive
- **Hydration Management**: Prevents hydration mismatches through consistent rendering strategies
- **Loading States**: Uses consistent loading states that work for both server and client rendering

## API Patterns

### Controller Organization
API controllers are organized following these patterns:

- **Resource-Based Controllers**: Controllers are based on resource types
- **Tenant-Aware Routing**: Routes include tenant identification
- **Standard CRUD Actions**: Consistent action methods across controllers
- **Response Wrapping**: Standardized response format for all endpoints

### Request/Response Patterns
API requests and responses follow these patterns:

- **DTO Pattern**: Data Transfer Objects for request and response data
- **Validation**: Request validation using model validation attributes
- **Standard Response Format**: Consistent response format with status, data, and message
- **Error Responses**: Standard error format for all error responses

## Data Models

### Entity Design
The system uses the following entity design patterns:

- **Base Entity**: Common properties for all entities (Id, Created, Modified)
- **Tenant-Aware Entities**: Entities with tenant identification
- **Navigation Properties**: Entity relationships expressed as navigation properties
- **Computed Properties**: Some properties are computed rather than stored (e.g., token expiration status)

### Data Relationships
Entity relationships follow these patterns:

- **One-to-Many**: Standard EF relationship with navigation properties
- **Many-to-Many**: Join tables with explicit join entities
- **Cascade Delete**: Controlled cascade delete for related entities
- **Tenant Isolation**: Relationships are constrained by tenant boundaries

## Security Patterns

### Authentication Security
The system implements several patterns to ensure authentication security:

- **JWT with Short Lifespan**: Access tokens expire quickly (15 minutes) to reduce risk
- **Refresh Token Rotation**: New refresh token issued with each refresh
- **Token Revocation**: Ability to revoke refresh tokens for security incidents
- **Secure Cookie Storage**: HTTP-only cookies for refresh tokens
- **CSRF Protection**: Combination of JWT in headers and cookies for CSRF protection

### Data Security 
Data security is maintained through:

- **Tenant Isolation**: Global query filters ensure data separation
- **Role-Based Access**: Permissions checked at controller and service levels
- **Input Validation**: All user input is validated before processing
- **SQL Injection Prevention**: Parameterized queries through EF Core
- **Field-Level Security**: DTOs control which fields are exposed

### Bot Agent Security
Bot agent security includes:

- **Machine Key Authentication**: Secure random keys for bot agent authentication
- **Explicit Asset Authorization**: Many-to-many relationships control asset access
- **Secure Local API**: Local API server for Python scripts to interact with the agent
- **Proper Authentication**: Machine keys instead of machine names for security

## Bot Agent Architecture

The Bot Agent is a distributed component that consists of:

- **Windows Service**: Background service for executing automation
- **WPF UI Application**: Configuration and monitoring interface
- **Local API Server**: Communication interface for Python scripts
- **SignalR Connection**: Real-time communication with the server

The Bot Agent communicates with the server using:

- **REST API**: For registration, asset retrieval, and management operations
- **SignalR**: For real-time status updates and command execution

The Bot Agent's security model includes:

- **Machine Key Authentication**: Cryptographically secure keys for server authentication
- **Asset Authorization**: Access to assets based on explicit relationships
- **Local API Security**: Binding to localhost only
- **Secure Credential Storage**: Protected storage for sensitive information 