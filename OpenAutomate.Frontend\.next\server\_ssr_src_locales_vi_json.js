"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_vi_json";
exports.ids = ["_ssr_src_locales_vi_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/vi.json":
/*!*****************************!*\
  !*** ./src/locales/vi.json ***!
  \*****************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"login":{"title":"Đăng nhập","description":"Nhập email và mật khẩu để tiếp tục","signupPrompt":"Chưa có tài khoản?","signupLink":"Đăng ký"},"button":{"filter":"Lọc","clear":"Làm mới"},"sectionCard":{"user":"Người dùng","schedules":"Lịch trình","assets":" Tài sản","agent":"Máy"}}');

/***/ })

};
;