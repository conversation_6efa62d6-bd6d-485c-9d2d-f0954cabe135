{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "OpenAutomate.API.Middleware.RequestLoggingMiddleware": "Debug", "Microsoft.AspNetCore.Mvc": "Information", "Microsoft.AspNetCore.Hosting": "Information"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "OpenAutomate.API.Middleware.RequestLoggingMiddleware": "Debug", "Microsoft.AspNetCore.Mvc": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}", "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"}}], "Enrich": ["FromLogContext", "WithThreadId"], "Properties": {"Application": "OpenAutomate.API"}}, "FrontendUrl": "http://localhost:3001", "AllowedHosts": "*", "AppSettings": {"Database": {"DefaultConnection": "Server=localhost;Database=OpenAutomateDb;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "Jwt": {"Secret": "YourSecretKeyHere_ThisShouldBeAtLeast32CharsLong", "Issuer": "OpenAutomate", "Audience": "OpenAutomateClients", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001"]}, "GoogleAuth": {"ClientId": "86404112264-r1p7n5rjdod3f3g7fftooi66m4k781u4.apps.googleusercontent.com", "ClientSecret": "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"}, "EmailSettings": {"SmtpServer": "email-smtp.ap-southeast-1.amazonaws.com", "Port": 2587, "EnableSsl": true, "SenderEmail": "<EMAIL>", "SenderName": "OpenAutomate", "Username": "********************", "Password": "BALCDiJjnUrvnZbqOFXusV1nLn1Q8Xt3rWL2CaLDN19t"}}, "AWS": {"Region": "ap-southeast-1", "BucketName": "openautomate-packages", "AccessKey": "********************", "SecretKey": "Aj1Pq7l0S/u5BDgbe6mnONT4MQIe04T1etJzQHgo", "PresignedUrlExpirationMinutes": 15}}