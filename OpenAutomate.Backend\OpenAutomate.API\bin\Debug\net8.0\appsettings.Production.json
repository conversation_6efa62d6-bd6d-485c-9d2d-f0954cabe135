{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "OpenAutomate.API.Middleware.RequestLoggingMiddleware": "Debug"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "OpenAutomate.API.Middleware.RequestLoggingMiddleware": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/openAutomate-api-.log", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}", "retainedFileCountLimit": 30, "fileSizeLimitBytes": 10485760, "shared": true, "flushToDiskInterval": "00:00:01"}}], "Enrich": ["FromLogContext", "WithThreadId", "WithMachineName"], "Properties": {"Application": "OpenAutomate.API", "Environment": "Production"}}, "FrontendUrl": "https://cloud.openautomate.me", "AllowedHosts": "*", "AppSettings": {"Database": {"DefaultConnection": "Server=**************,1433;Database=OpenAutomateDb;User Id=sa;Password=openAutomate@12345;MultipleActiveResultSets=true;TrustServerCertificate=True"}, "Jwt": {"Secret": "YourSecretKeyHere_ThisShouldBeAtLeast32CharsLong", "Issuer": "OpenAutomate", "Audience": "OpenAutomateClients", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Cors": {"AllowedOrigins": ["https://openautomate.me", "https://dev.openautomate.me", "https://api.openautomate.me", "http://localhost:3001", "https://cloud.openautomate.me"]}, "EmailSettings": {"SmtpServer": "email-smtp.ap-southeast-1.amazonaws.com", "Port": 2587, "EnableSsl": true, "SenderEmail": "<EMAIL>", "SenderName": "OpenAutomate", "Username": "********************", "Password": "BALCDiJjnUrvnZbqOFXusV1nLn1Q8Xt3rWL2CaLDN19t"}}, "AWS": {"Region": "ap-southeast-1", "BucketName": "openautomate-packages", "AccessKey": "********************", "SecretKey": "Aj1Pq7l0S/u5BDgbe6mnONT4MQIe04T1etJzQHgo", "PresignedUrlExpirationMinutes": 15}}