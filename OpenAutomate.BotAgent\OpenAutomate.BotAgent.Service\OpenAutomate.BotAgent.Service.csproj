<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    <UserSecretsId>dotnet-OpenAutomate.BotAgent.Service-e1f75e5d-8c43-490f-8c51-8e5d6ca3a2aa</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Connections" Version="1.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Routing" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="System.Security.Cryptography.ProtectedData" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OpenAutomate.BotAgent.Executor\OpenAutomate.BotAgent.Executor.csproj" />
  </ItemGroup>

</Project>
