self.__BUILD_MANIFEST = (function(a,b,c,d,e){return {__rewrites:{afterFiles:[{has:[{type:b,key:c}],source:d,destination:a},{has:a,source:d,destination:a},{has:[{type:b,key:c}],source:e,destination:a},{has:a,source:e,destination:a},{has:a,source:"\u002F:nextInternalLocale(en|vi)\u002F:tenantSlug\u002Fapi\u002F:path*",destination:a}],beforeFiles:[],fallback:[]},__routerFilterStatic:a,__routerFilterDynamic:a,sortedPages:["\u002F_app"]}}(void 0,"query","machineKey","\u002F:nextInternalLocale(en|vi)\u002F:tenantSlug\u002Fhubs\u002F:path*\u002Fnegotiate","\u002F:nextInternalLocale(en|vi)\u002F:tenantSlug\u002Fhubs\u002F:path*"));self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()