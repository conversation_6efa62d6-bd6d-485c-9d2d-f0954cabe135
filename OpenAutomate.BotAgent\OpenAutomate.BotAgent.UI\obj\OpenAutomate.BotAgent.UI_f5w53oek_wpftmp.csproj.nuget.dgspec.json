{"format": 1, "restore": {"D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.UI\\OpenAutomate.BotAgent.UI.csproj": {}}, "projects": {"D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj", "projectName": "OpenAutomate.BotAgent.Executor", "projectPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Service\\OpenAutomate.BotAgent.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Service\\OpenAutomate.BotAgent.Service.csproj", "projectName": "OpenAutomate.BotAgent.Service", "projectPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Service\\OpenAutomate.BotAgent.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj": {"projectPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Hosting": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Http.Connections": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.AspNetCore.Routing": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.AspNetCore.SignalR.Core": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Security.Cryptography.ProtectedData": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.UI\\OpenAutomate.BotAgent.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.UI\\OpenAutomate.BotAgent.UI.csproj", "projectName": "OpenAutomate.BotAgent.UI", "projectPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.UI\\OpenAutomate.BotAgent.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.UI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Service\\OpenAutomate.BotAgent.Service.csproj": {"projectPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Service\\OpenAutomate.BotAgent.Service.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.1, )"}, "System.Net.Http.Json": {"target": "Package", "version": "[8.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}