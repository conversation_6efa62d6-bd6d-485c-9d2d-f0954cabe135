{"format": 1, "restore": {"D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\OpenAutomate.API.csproj": {}}, "projects": {"D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\OpenAutomate.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\OpenAutomate.API.csproj", "projectName": "OpenAutomate.API", "projectPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\OpenAutomate.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj": {"projectPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj"}, "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj": {"projectPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.14, )"}, "Microsoft.AspNetCore.OData": {"target": "Package", "version": "[8.2.3, )"}, "Microsoft.IdentityModel.Protocols": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.3.0, )"}, "Serilog.Enrichers.Process": {"target": "Package", "version": "[2.0.2, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Extensions.Logging.File": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj", "projectName": "OpenAutomate.Core", "projectPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj", "projectName": "OpenAutomate.Infrastructure", "projectPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj": {"projectPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.307.25, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}