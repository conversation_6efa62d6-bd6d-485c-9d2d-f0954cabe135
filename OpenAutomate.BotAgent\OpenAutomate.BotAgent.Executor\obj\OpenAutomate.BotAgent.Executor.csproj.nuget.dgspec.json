{"format": 1, "restore": {"D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj": {}}, "projects": {"D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj", "projectName": "OpenAutomate.BotAgent.Executor", "projectPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\OpenAutomate.BotAgent.Executor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\CapstoneProject\\OpenAutomate.BotAgent\\OpenAutomate.BotAgent.Executor\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}