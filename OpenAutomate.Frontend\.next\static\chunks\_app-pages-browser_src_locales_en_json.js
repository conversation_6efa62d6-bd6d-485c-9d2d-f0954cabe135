"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_json"],{

/***/ "(app-pages-browser)/./src/locales/en.json":
/*!*****************************!*\
  !*** ./src/locales/en.json ***!
  \*****************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

module.exports = /*#__PURE__*/JSON.parse('{"login":{"title":"Sign In","description":"Enter your email and password to continue","signupPrompt":"Don\'t have an account?","signupLink":"Sign up"},"button":{"filter":"Filter","clear":"Clear"},"sectionCard":{"user":"User","schedules":"Schedules","assets":"Assets","agent":"Agent"}}');

/***/ })

}]);