{"Version": 1, "WorkspaceRootPath": "D:\\CapstoneProject\\OpenAutomate.Backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\tenantcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\tenantcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\repositories\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\repositories\\unitofwork.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\repositories\\repository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\repositories\\repository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\organizationunitservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\organizationunitservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\organizationunitcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\organizationunitcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\middleware\\tenantresolutionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\middleware\\tenantresolutionmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\middleware\\requestloggingmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\middleware\\requestloggingmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{56506789-D4BF-4A98-B68D-264199D44F91}|OpenAutomate.Infrastructure.Tests\\OpenAutomate.Infrastructure.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure.tests\\repositories\\assetrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{56506789-D4BF-4A98-B68D-264199D44F91}|OpenAutomate.Infrastructure.Tests\\OpenAutomate.Infrastructure.Tests.csproj|solutionrelative:openautomate.infrastructure.tests\\repositories\\assetrepositorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\assetcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\assetcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\openautomate.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\openautomate.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{56506789-D4BF-4A98-B68D-264199D44F91}|OpenAutomate.Infrastructure.Tests\\OpenAutomate.Infrastructure.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure.tests\\testresults\\29fbc324-69d3-4360-a53c-58d149f0e1b0\\coverage.cobertura.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{56506789-D4BF-4A98-B68D-264199D44F91}|OpenAutomate.Infrastructure.Tests\\OpenAutomate.Infrastructure.Tests.csproj|solutionrelative:openautomate.infrastructure.tests\\testresults\\29fbc324-69d3-4360-a53c-58d149f0e1b0\\coverage.cobertura.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\tenantqueryfilterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\tenantqueryfilterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\botagentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\botagentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\odata\\botagentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\odata\\botagentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\dbcontext\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\dbcontext\\applicationdbcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\organizationunit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\organizationunit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\middleware\\jwtauthenticationmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\middleware\\jwtauthenticationmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\tokenservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\tokenservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\hubs\\botagenthub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\hubs\\botagenthub.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\odata\\assetscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\odata\\assetscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\botagentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\botagentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\botagentassetcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\botagentassetcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\iservices\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\iservices\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core.tests\\openautomate.core.tests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|solutionrelative:openautomate.core.tests\\openautomate.core.tests.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\customcontrollerbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\customcontrollerbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\botagentconnectioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\botagentconnectioncontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\botagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\botagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\assetservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\assetservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\adminservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\adminservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\admincontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\exceptions\\openautomateexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\exceptions\\openautomateexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\exceptions\\assetexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\exceptions\\assetexception.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\openautomate.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\openautomate.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core.tests\\domaintests\\assettest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|solutionrelative:openautomate.core.tests\\domaintests\\assettest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core.tests\\domaintests\\assetbotagenttest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|solutionrelative:openautomate.core.tests\\domaintests\\assetbotagenttest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0192DCE8-78C1-4EF7-872F-3E4973797A91}|OpenAutomate.API.Tests\\OpenAutomate.API.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api.tests\\controllertests\\botagentcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0192DCE8-78C1-4EF7-872F-3E4973797A91}|OpenAutomate.API.Tests\\OpenAutomate.API.Tests.csproj|solutionrelative:openautomate.api.tests\\controllertests\\botagentcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0192DCE8-78C1-4EF7-872F-3E4973797A91}|OpenAutomate.API.Tests\\OpenAutomate.API.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api.tests\\controllertests\\assetcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0192DCE8-78C1-4EF7-872F-3E4973797A91}|OpenAutomate.API.Tests\\OpenAutomate.API.Tests.csproj|solutionrelative:openautomate.api.tests\\controllertests\\assetcontrollertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\assetbotagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\assetbotagent.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\asset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\asset.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\asset\\createassetdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\asset\\createassetdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\asset\\updateassetdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\asset\\updateassetdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\assetconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\assetconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\asset\\assetlistresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\asset\\assetlistresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\asset\\assetbotagentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\asset\\assetbotagentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\authencontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\authencontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\emailtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\emailtestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\emailverificationtoken.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\emailverificationtoken.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\1fe2286f2032997fc4d4940e6875deb153628a11212a387cf5b94c59c34836f3\\OpenAutomate.Infrastructure\\Services\\TenantContext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\iservices\\ibotagentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\iservices\\ibotagentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\emailverificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\emailverificationcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\controllers\\authorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\controllers\\authorcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\botagent\\botagentresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\botagent\\botagentresponsedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\botagent\\createbotagentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\botagent\\createbotagentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\botagent\\botagentconnectionrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\botagent\\botagentconnectionrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\scheduleconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\scheduleconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\userconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\refreshtoken.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\refreshtoken.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\schedule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\schedule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\dto\\userdto\\authenticationresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\dto\\userdto\\authenticationresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\userauthority.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\userauthority.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.infrastructure\\services\\awssesemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0AB8D80C-BB3D-48F8-997C-7F2727D29BB5}|OpenAutomate.Infrastructure\\OpenAutomate.Infrastructure.csproj|solutionrelative:openautomate.infrastructure\\services\\awssesemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\iservices\\iemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\iservices\\iemailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\domain\\entities\\organizationunituser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\domain\\entities\\organizationunituser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.api\\attributes\\allowanonymousattribute.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C80AAAC-DFA9-4DB4-8404-B1FEE7742067}|OpenAutomate.API\\OpenAutomate.API.csproj|solutionrelative:openautomate.api\\attributes\\allowanonymousattribute.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\authorityconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\authorityconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\authorityresourceconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\authorityresourceconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\corssettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\corssettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core\\configurations\\packageversionconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D1B02A75-A815-45CD-BED2-22349B1889DA}|OpenAutomate.Core\\OpenAutomate.Core.csproj|solutionrelative:openautomate.core\\configurations\\packageversionconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core.tests\\servicetests\\tokenservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|solutionrelative:openautomate.core.tests\\servicetests\\tokenservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core.tests\\servicetests\\userservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|solutionrelative:openautomate.core.tests\\servicetests\\userservicetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|d:\\capstoneproject\\openautomate.backend\\openautomate.core.tests\\domaintests\\usertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{949DC82C-6B32-482F-8836-B50FD93CE18F}|OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj|solutionrelative:openautomate.core.tests\\domaintests\\usertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 57, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{269a02dc-6af8-11d3-bdc4-00c04f688e50}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Document", "DocumentIndex": 9, "Title": "RequestLoggingMiddleware.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Middleware\\RequestLoggingMiddleware.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Middleware\\RequestLoggingMiddleware.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Middleware\\RequestLoggingMiddleware.cs", "RelativeToolTip": "OpenAutomate.API\\Middleware\\RequestLoggingMiddleware.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T03:33:02.337Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AssetRepositoryTests.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure.Tests\\Repositories\\AssetRepositoryTests.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure.Tests\\Repositories\\AssetRepositoryTests.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure.Tests\\Repositories\\AssetRepositoryTests.cs", "RelativeToolTip": "OpenAutomate.Infrastructure.Tests\\Repositories\\AssetRepositoryTests.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAYwBcAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-22T18:14:35.352Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Preview\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAAEoTAAAAAAAAAAD4v+UTAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-05-21T03:20:26.619Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "OrganizationUnitService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\OrganizationUnitService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\OrganizationUnitService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\OrganizationUnitService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\OrganizationUnitService.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAHIAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-18T13:42:49.744Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "OpenAutomate.Core.Tests.csproj", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj", "RelativeDocumentMoniker": "OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj", "RelativeToolTip": "OpenAutomate.Core.Tests\\OpenAutomate.Core.Tests.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-18T13:42:25.416Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "IUserservice.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\IServices\\IUserservice.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\IServices\\IUserservice.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\IServices\\IUserservice.cs", "RelativeToolTip": "OpenAutomate.Core\\IServices\\IUserservice.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAuwBYAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-18T13:41:43.557Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "JwtAuthenticationMiddleware.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Middleware\\JwtAuthenticationMiddleware.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Middleware\\JwtAuthenticationMiddleware.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Middleware\\JwtAuthenticationMiddleware.cs", "RelativeToolTip": "OpenAutomate.API\\Middleware\\JwtAuthenticationMiddleware.cs", "ViewState": "AgIAABcAAAAAAAAAAAAawDAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-17T19:01:14.864Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "BotAgentAssetController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\BotAgentAssetController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\BotAgentAssetController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\BotAgentAssetController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\BotAgentAssetController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-17T18:31:43.511Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "BotAgentConnectionController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\BotAgentConnectionController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\BotAgentConnectionController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\BotAgentConnectionController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\BotAgentConnectionController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAANAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-17T18:30:12.323Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "BotAgentHub.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Hubs\\BotAgentHub.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Hubs\\BotAgentHub.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Hubs\\BotAgentHub.cs", "RelativeToolTip": "OpenAutomate.API\\Hubs\\BotAgentHub.cs", "ViewState": "AgIAAOAAAAAAAAAAAAAcwAwBAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-17T13:55:40.698Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "AssetsController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\OData\\AssetsController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\OData\\AssetsController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\OData\\AssetsController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\OData\\AssetsController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABMAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T14:34:18.913Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "BotAgentsController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\OData\\BotAgentsController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\OData\\BotAgentsController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\OData\\BotAgentsController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\OData\\BotAgentsController.cs", "ViewState": "AgIAACYAAAAAAAAAAAAEwDYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T14:33:55.133Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "AdminService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\AdminService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\AdminService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\AdminService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\AdminService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T14:33:21.805Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "OpenAutomateException.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Exceptions\\OpenAutomateException.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Exceptions\\OpenAutomateException.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Exceptions\\OpenAutomateException.cs", "RelativeToolTip": "OpenAutomate.Core\\Exceptions\\OpenAutomateException.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADMAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T14:32:52.107Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "AssetException.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Exceptions\\AssetException.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Exceptions\\AssetException.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Exceptions\\AssetException.cs", "RelativeToolTip": "OpenAutomate.Core\\Exceptions\\AssetException.cs", "ViewState": "AgIAADAAAAAAAAAAAAAkwDgAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T14:32:42.443Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "OpenAutomate.API", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\OpenAutomate.API.csproj", "RelativeDocumentMoniker": "OpenAutomate.API\\OpenAutomate.API.csproj", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\OpenAutomate.API.csproj", "RelativeToolTip": "OpenAutomate.API\\OpenAutomate.API.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-15T06:45:11.52Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "AssetTest.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\DomainTests\\AssetTest.cs", "RelativeDocumentMoniker": "OpenAutomate.Core.Tests\\DomainTests\\AssetTest.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\DomainTests\\AssetTest.cs", "RelativeToolTip": "OpenAutomate.Core.Tests\\DomainTests\\AssetTest.cs", "ViewState": "AgIAAEAAAAAAAAAAAAA0wB8AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T19:06:15.855Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "AssetBotAgentTest.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\DomainTests\\AssetBotAgentTest.cs", "RelativeDocumentMoniker": "OpenAutomate.Core.Tests\\DomainTests\\AssetBotAgentTest.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\DomainTests\\AssetBotAgentTest.cs", "RelativeToolTip": "OpenAutomate.Core.Tests\\DomainTests\\AssetBotAgentTest.cs", "ViewState": "AgIAAA4AAAAAAAAAAADwvxoAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T19:05:54.513Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "AssetControllerTests.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "RelativeDocumentMoniker": "OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "RelativeToolTip": "OpenAutomate.API.Tests\\ControllerTests\\AssetControllerTests.cs", "ViewState": "AgIAALcAAAAAAAAAAADwv8YAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T19:02:02.307Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "AssetBotAgent.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\AssetBotAgent.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\AssetBotAgent.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\AssetBotAgent.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\AssetBotAgent.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:35:29.486Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "Asset.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\Asset.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\Asset.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\Asset.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\Asset.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:35:19.855Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "UpdateAssetDto.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\UpdateAssetDto.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\Asset\\UpdateAssetDto.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\UpdateAssetDto.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\Asset\\UpdateAssetDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:30:22.317Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "AssetService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\AssetService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\AssetService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\AssetService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\AssetService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAOsAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:26:47.582Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "CreateAssetDto.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\CreateAssetDto.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\Asset\\CreateAssetDto.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\CreateAssetDto.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\Asset\\CreateAssetDto.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAC0AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:16:29.236Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "AssetListResponseDto.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\AssetListResponseDto.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\Asset\\AssetListResponseDto.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\AssetListResponseDto.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\Asset\\AssetListResponseDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:15:58.211Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "AssetBotAgentDto.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\AssetBotAgentDto.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\Asset\\AssetBotAgentDto.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\Asset\\AssetBotAgentDto.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\Asset\\AssetBotAgentDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:15:53.206Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "AssetController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AssetController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\AssetController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AssetController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\AssetController.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAYwB8AAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T16:15:07.355Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "AdminController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AdminController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\AdminController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AdminController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\AdminController.cs", "ViewState": "AgIAAFkAAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:53:02.006Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "BotAgentControllerTests.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API.Tests\\ControllerTests\\BotAgentControllerTests.cs", "RelativeDocumentMoniker": "OpenAutomate.API.Tests\\ControllerTests\\BotAgentControllerTests.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API.Tests\\ControllerTests\\BotAgentControllerTests.cs", "RelativeToolTip": "OpenAutomate.API.Tests\\ControllerTests\\BotAgentControllerTests.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:19:21.526Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "EmailVerificationToken.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\EmailVerificationToken.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\EmailVerificationToken.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\EmailVerificationToken.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\EmailVerificationToken.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T20:11:39.605Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "UnitOfWork.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Repositories\\UnitOfWork.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Repositories\\UnitOfWork.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Repositories\\UnitOfWork.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Repositories\\UnitOfWork.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T19:42:32.239Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "TenantContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\1fe2286f2032997fc4d4940e6875deb153628a11212a387cf5b94c59c34836f3\\OpenAutomate.Infrastructure\\Services\\TenantContext.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\1fe2286f2032997fc4d4940e6875deb153628a11212a387cf5b94c59c34836f3\\OpenAutomate.Infrastructure\\Services\\TenantContext.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-08T16:17:02.984Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "UserController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\UserController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\UserController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\UserController.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T19:05:19.735Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "BotAgentService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\BotAgentService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\BotAgentService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\BotAgentService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\BotAgentService.cs", "ViewState": "AgIAACQAAAAAAAAAAAAAAFYAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T15:53:08.466Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "IBotAgentService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\IServices\\IBotAgentService.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\IServices\\IBotAgentService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\IServices\\IBotAgentService.cs", "RelativeToolTip": "OpenAutomate.Core\\IServices\\IBotAgentService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T15:51:39Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Repository.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Repositories\\Repository.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Repositories\\Repository.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Repositories\\Repository.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Repositories\\Repository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-07T07:26:32.703Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "CreateBotAgentDto.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\BotAgent\\CreateBotAgentDto.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\BotAgent\\CreateBotAgentDto.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\BotAgent\\CreateBotAgentDto.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\BotAgent\\CreateBotAgentDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-06T15:11:02.608Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "BotAgentResponseDto.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\BotAgent\\BotAgentResponseDto.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\BotAgent\\BotAgentResponseDto.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\BotAgent\\BotAgentResponseDto.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\BotAgent\\BotAgentResponseDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-06T15:10:55.992Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "BotAgentConnectionRequest.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\BotAgent\\BotAgentConnectionRequest.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\BotAgent\\BotAgentConnectionRequest.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\BotAgent\\BotAgentConnectionRequest.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\BotAgent\\BotAgentConnectionRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-06T15:10:44.039Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.Production.json", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Production.json", "RelativeDocumentMoniker": "OpenAutomate.API\\appsettings.Production.json", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Production.json", "RelativeToolTip": "OpenAutomate.API\\appsettings.Production.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-05T13:54:49.794Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "coverage.cobertura.xml", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure.Tests\\TestResults\\29fbc324-69d3-4360-a53c-58d149f0e1b0\\coverage.cobertura.xml", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure.Tests\\TestResults\\29fbc324-69d3-4360-a53c-58d149f0e1b0\\coverage.cobertura.xml", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure.Tests\\TestResults\\29fbc324-69d3-4360-a53c-58d149f0e1b0\\coverage.cobertura.xml", "RelativeToolTip": "OpenAutomate.Infrastructure.Tests\\TestResults\\29fbc324-69d3-4360-a53c-58d149f0e1b0\\coverage.cobertura.xml", "ViewState": "AgIAANYHAAAAAAAAAAAAAPAHAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-05-01T11:48:10.278Z"}, {"$type": "Document", "DocumentIndex": 76, "Title": "TokenServiceTests.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\ServiceTests\\TokenServiceTests.cs", "RelativeDocumentMoniker": "OpenAutomate.Core.Tests\\ServiceTests\\TokenServiceTests.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\ServiceTests\\TokenServiceTests.cs", "RelativeToolTip": "OpenAutomate.Core.Tests\\ServiceTests\\TokenServiceTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAH4AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-01T11:46:20.391Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "launchSettings.json", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "OpenAutomate.API\\Properties\\launchSettings.json", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Properties\\launchSettings.json", "RelativeToolTip": "OpenAutomate.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-01T09:05:54.849Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "RefreshToken.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\RefreshToken.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\RefreshToken.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\RefreshToken.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\RefreshToken.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwAsAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T19:26:05.121Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "ScheduleConfiguration.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\ScheduleConfiguration.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\ScheduleConfiguration.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\ScheduleConfiguration.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\ScheduleConfiguration.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAwwBEAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T19:26:40.938Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "Schedule.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\Schedule.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\Schedule.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\Schedule.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\Schedule.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T19:26:05.424Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "UserConfiguration.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\UserConfiguration.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\UserConfiguration.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\UserConfiguration.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\UserConfiguration.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAcwBwAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T19:25:26.473Z"}, {"$type": "Document", "DocumentIndex": 77, "Title": "UserServiceTests.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\ServiceTests\\UserServiceTests.cs", "RelativeDocumentMoniker": "OpenAutomate.Core.Tests\\ServiceTests\\UserServiceTests.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\ServiceTests\\UserServiceTests.cs", "RelativeToolTip": "OpenAutomate.Core.Tests\\ServiceTests\\UserServiceTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T14:23:32.365Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "TenantResolutionMiddleware.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Middleware\\TenantResolutionMiddleware.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Middleware\\TenantResolutionMiddleware.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Middleware\\TenantResolutionMiddleware.cs", "RelativeToolTip": "OpenAutomate.API\\Middleware\\TenantResolutionMiddleware.cs", "ViewState": "AgIAABYAAAAAAAAAAAAEwCkAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T13:11:44.005Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "EmailVerificationController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\EmailVerificationController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\EmailVerificationController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\EmailVerificationController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\EmailVerificationController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAD4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T13:02:16.171Z"}, {"$type": "Document", "DocumentIndex": 78, "Title": "UserTests.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\DomainTests\\UserTests.cs", "RelativeDocumentMoniker": "OpenAutomate.Core.Tests\\DomainTests\\UserTests.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core.Tests\\DomainTests\\UserTests.cs", "RelativeToolTip": "OpenAutomate.Core.Tests\\DomainTests\\UserTests.cs", "ViewState": "AgIAABMAAAAAAAAAAAAgwBUAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-26T11:34:27.538Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "AuthenticationResponse.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\UserDto\\AuthenticationResponse.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Dto\\UserDto\\AuthenticationResponse.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Dto\\UserDto\\AuthenticationResponse.cs", "RelativeToolTip": "OpenAutomate.Core\\Dto\\UserDto\\AuthenticationResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAIBJwBIAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-25T17:47:29.265Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "User.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\User.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\User.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\User.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\User.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAnwBYAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-25T10:23:10.087Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "UserAuthority.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\UserAuthority.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\UserAuthority.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\UserAuthority.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\UserAuthority.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-25T10:23:09.458Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "AwsSesEmailService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\AwsSesEmailService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\AwsSesEmailService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\AwsSesEmailService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\AwsSesEmailService.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T17:39:47.059Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "TenantContext.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\TenantContext.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\TenantContext.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\TenantContext.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\TenantContext.cs", "ViewState": "AgIAADIAAAAAAAAAAAAtwDwAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T17:39:42.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "TenantQueryFilterService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\TenantQueryFilterService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\TenantQueryFilterService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\TenantQueryFilterService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\TenantQueryFilterService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T17:39:07.791Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "TokenService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\TokenService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\TokenService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\TokenService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\TokenService.cs", "ViewState": "AgIAANEAAAAAAAAAAAAawOgAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T17:39:07.018Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "UserService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\UserService.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\Services\\UserService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\Services\\UserService.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\Services\\UserService.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAADwBAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T17:39:06.491Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "IEmailService.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\IServices\\IEmailService.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\IServices\\IEmailService.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\IServices\\IEmailService.cs", "RelativeToolTip": "OpenAutomate.Core\\IServices\\IEmailService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T17:38:53.345Z"}, {"$type": "Document", "DocumentIndex": 70, "Title": "OrganizationUnitUser.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\OrganizationUnitUser.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\OrganizationUnitUser.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\OrganizationUnitUser.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\OrganizationUnitUser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T15:38:11.951Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "OrganizationUnit.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\OrganizationUnit.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\OrganizationUnit.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\OrganizationUnit.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\OrganizationUnit.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABIAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T15:37:06.698Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "BotAgent.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\BotAgent.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Domain\\Entities\\BotAgent.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Domain\\Entities\\BotAgent.cs", "RelativeToolTip": "OpenAutomate.Core\\Domain\\Entities\\BotAgent.cs", "ViewState": "AgIAABYAAAAAAAAAAAAgwDUAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T15:36:10.598Z"}, {"$type": "Document", "DocumentIndex": 71, "Title": "AllowAnonymousAttribute.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Attributes\\AllowAnonymousAttribute.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Attributes\\AllowAnonymousAttribute.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Attributes\\AllowAnonymousAttribute.cs", "RelativeToolTip": "OpenAutomate.API\\Attributes\\AllowAnonymousAttribute.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T14:05:27.176Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "AssetConfiguration.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\AssetConfiguration.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\AssetConfiguration.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\AssetConfiguration.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\AssetConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:52.597Z"}, {"$type": "Document", "DocumentIndex": 72, "Title": "AuthorityConfiguration.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\AuthorityConfiguration.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\AuthorityConfiguration.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\AuthorityConfiguration.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\AuthorityConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:51.105Z"}, {"$type": "Document", "DocumentIndex": 73, "Title": "AuthorityResourceConfiguration.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\AuthorityResourceConfiguration.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\AuthorityResourceConfiguration.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\AuthorityResourceConfiguration.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\AuthorityResourceConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:47.897Z"}, {"$type": "Document", "DocumentIndex": 74, "Title": "CorsSettings.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\CorsSettings.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\CorsSettings.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\CorsSettings.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\CorsSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:46.766Z"}, {"$type": "Document", "DocumentIndex": 75, "Title": "PackageVersionConfiguration.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\PackageVersionConfiguration.cs", "RelativeDocumentMoniker": "OpenAutomate.Core\\Configurations\\PackageVersionConfiguration.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\Configurations\\PackageVersionConfiguration.cs", "RelativeToolTip": "OpenAutomate.Core\\Configurations\\PackageVersionConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:44.308Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ApplicationDbContext.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\DbContext\\ApplicationDbContext.cs", "RelativeDocumentMoniker": "OpenAutomate.Infrastructure\\DbContext\\ApplicationDbContext.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Infrastructure\\DbContext\\ApplicationDbContext.cs", "RelativeToolTip": "OpenAutomate.Infrastructure\\DbContext\\ApplicationDbContext.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAawF8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:29.164Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "Program.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Program.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Program.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Program.cs", "RelativeToolTip": "OpenAutomate.API\\Program.cs", "ViewState": "AgIAAPoAAAAAAAAAAAAAACoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:55:11.272Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrganizationUnitController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\OrganizationUnitController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\OrganizationUnitController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\OrganizationUnitController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\OrganizationUnitController.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAQwGEAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:53:58.092Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "AuthorController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AuthorController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\AuthorController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AuthorController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\AuthorController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAJYAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:53:42.936Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "AuthenController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AuthenController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\AuthenController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\AuthenController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\AuthenController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAKgBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:53:41.957Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "EmailTestController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\EmailTestController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\EmailTestController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\EmailTestController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\EmailTestController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:53:35.147Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "CustomControllerBase.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\CustomControllerBase.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\CustomControllerBase.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\CustomControllerBase.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\CustomControllerBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:53:33.599Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "BotAgentController.cs", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\BotAgentController.cs", "RelativeDocumentMoniker": "OpenAutomate.API\\Controllers\\BotAgentController.cs", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\Controllers\\BotAgentController.cs", "RelativeToolTip": "OpenAutomate.API\\Controllers\\BotAgentController.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAqwGEAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-23T13:52:59.661Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "appsettings.Development.json", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Development.json", "RelativeDocumentMoniker": "OpenAutomate.API\\appsettings.Development.json", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.API\\appsettings.Development.json", "RelativeToolTip": "OpenAutomate.API\\appsettings.Development.json", "ViewState": "AgIAAAMAAAAAAAAAAAAAAA0AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-04-22T19:52:55.983Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "OpenAutomate.Core.csproj", "DocumentMoniker": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj", "RelativeDocumentMoniker": "OpenAutomate.Core\\OpenAutomate.Core.csproj", "ToolTip": "D:\\CapstoneProject\\OpenAutomate.Backend\\OpenAutomate.Core\\OpenAutomate.Core.csproj", "RelativeToolTip": "OpenAutomate.Core\\OpenAutomate.Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-04-22T19:52:45.775Z"}]}]}]}