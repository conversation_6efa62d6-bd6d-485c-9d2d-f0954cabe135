"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@microsoft";
exports.ids = ["vendor-chunks/@microsoft"];
exports.modules = {

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/AbortController.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/AbortController.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortController: () => (/* binding */ AbortController)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n// Not exported from index.\r\n/** @private */\r\nclass AbortController {\r\n    constructor() {\r\n        this._isAborted = false;\r\n        this.onabort = null;\r\n    }\r\n    abort() {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n    get signal() {\r\n        return this;\r\n    }\r\n    get aborted() {\r\n        return this._isAborted;\r\n    }\r\n}\r\n//# sourceMappingURL=AbortController.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC9zaWduYWxyL2Rpc3QvZXNtL0Fib3J0Q29udHJvbGxlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDYXBzdG9uZVByb2plY3RcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAbWljcm9zb2Z0XFxzaWduYWxyXFxkaXN0XFxlc21cXEFib3J0Q29udHJvbGxlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMaWNlbnNlZCB0byB0aGUgLk5FVCBGb3VuZGF0aW9uIHVuZGVyIG9uZSBvciBtb3JlIGFncmVlbWVudHMuXHJcbi8vIFRoZSAuTkVUIEZvdW5kYXRpb24gbGljZW5zZXMgdGhpcyBmaWxlIHRvIHlvdSB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXHJcbi8vIFJvdWdoIHBvbHlmaWxsIG9mIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0FQSS9BYm9ydENvbnRyb2xsZXJcclxuLy8gV2UgZG9uJ3QgYWN0dWFsbHkgZXZlciB1c2UgdGhlIEFQSSBiZWluZyBwb2x5ZmlsbGVkLCB3ZSBhbHdheXMgdXNlIHRoZSBwb2x5ZmlsbCBiZWNhdXNlXHJcbi8vIGl0J3MgYSB2ZXJ5IG5ldyBBUEkgcmlnaHQgbm93LlxyXG4vLyBOb3QgZXhwb3J0ZWQgZnJvbSBpbmRleC5cclxuLyoqIEBwcml2YXRlICovXHJcbmV4cG9ydCBjbGFzcyBBYm9ydENvbnRyb2xsZXIge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgdGhpcy5faXNBYm9ydGVkID0gZmFsc2U7XHJcbiAgICAgICAgdGhpcy5vbmFib3J0ID0gbnVsbDtcclxuICAgIH1cclxuICAgIGFib3J0KCkge1xyXG4gICAgICAgIGlmICghdGhpcy5faXNBYm9ydGVkKSB7XHJcbiAgICAgICAgICAgIHRoaXMuX2lzQWJvcnRlZCA9IHRydWU7XHJcbiAgICAgICAgICAgIGlmICh0aGlzLm9uYWJvcnQpIHtcclxuICAgICAgICAgICAgICAgIHRoaXMub25hYm9ydCgpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgZ2V0IHNpZ25hbCgpIHtcclxuICAgICAgICByZXR1cm4gdGhpcztcclxuICAgIH1cclxuICAgIGdldCBhYm9ydGVkKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLl9pc0Fib3J0ZWQ7XHJcbiAgICB9XHJcbn1cclxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QWJvcnRDb250cm9sbGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/AbortController.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessTokenHttpClient: () => (/* binding */ AccessTokenHttpClient)\n/* harmony export */ });\n/* harmony import */ var _HeaderNames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./HeaderNames */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HeaderNames.js\");\n/* harmony import */ var _HttpClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpClient.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n/** @private */\r\nclass AccessTokenHttpClient extends _HttpClient__WEBPACK_IMPORTED_MODULE_0__.HttpClient {\r\n    constructor(innerClient, accessTokenFactory) {\r\n        super();\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n    async send(request) {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n    _setAuthorizationHeader(request) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[_HeaderNames__WEBPACK_IMPORTED_MODULE_1__.HeaderNames.Authorization] = `Bearer ${this._accessToken}`;\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[_HeaderNames__WEBPACK_IMPORTED_MODULE_1__.HeaderNames.Authorization]) {\r\n                delete request.headers[_HeaderNames__WEBPACK_IMPORTED_MODULE_1__.HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n    getCookieString(url) {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}\r\n//# sourceMappingURL=AccessTokenHttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/DefaultHttpClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/DefaultHttpClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultHttpClient: () => (/* binding */ DefaultHttpClient)\n/* harmony export */ });\n/* harmony import */ var _Errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Errors */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\");\n/* harmony import */ var _FetchHttpClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FetchHttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js\");\n/* harmony import */ var _HttpClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpClient.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n/* harmony import */ var _XhrHttpClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./XhrHttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nclass DefaultHttpClient extends _HttpClient__WEBPACK_IMPORTED_MODULE_0__.HttpClient {\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    constructor(logger) {\r\n        super();\r\n        if (typeof fetch !== \"undefined\" || _Utils__WEBPACK_IMPORTED_MODULE_1__.Platform.isNode) {\r\n            this._httpClient = new _FetchHttpClient__WEBPACK_IMPORTED_MODULE_2__.FetchHttpClient(logger);\r\n        }\r\n        else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new _XhrHttpClient__WEBPACK_IMPORTED_MODULE_3__.XhrHttpClient(logger);\r\n        }\r\n        else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n    /** @inheritDoc */\r\n    send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_4__.AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return this._httpClient.send(request);\r\n    }\r\n    getCookieString(url) {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n//# sourceMappingURL=DefaultHttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/DefaultHttpClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultReconnectPolicy: () => (/* binding */ DefaultReconnectPolicy)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n/** @private */\r\nclass DefaultReconnectPolicy {\r\n    constructor(retryDelays) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n    nextRetryDelayInMilliseconds(retryContext) {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n//# sourceMappingURL=DefaultReconnectPolicy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC9zaWduYWxyL2Rpc3QvZXNtL0RlZmF1bHRSZWNvbm5lY3RQb2xpY3kuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxDYXBzdG9uZVByb2plY3RcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAbWljcm9zb2Z0XFxzaWduYWxyXFxkaXN0XFxlc21cXERlZmF1bHRSZWNvbm5lY3RQb2xpY3kuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTGljZW5zZWQgdG8gdGhlIC5ORVQgRm91bmRhdGlvbiB1bmRlciBvbmUgb3IgbW9yZSBhZ3JlZW1lbnRzLlxyXG4vLyBUaGUgLk5FVCBGb3VuZGF0aW9uIGxpY2Vuc2VzIHRoaXMgZmlsZSB0byB5b3UgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxyXG4vLyAwLCAyLCAxMCwgMzAgc2Vjb25kIGRlbGF5cyBiZWZvcmUgcmVjb25uZWN0IGF0dGVtcHRzLlxyXG5jb25zdCBERUZBVUxUX1JFVFJZX0RFTEFZU19JTl9NSUxMSVNFQ09ORFMgPSBbMCwgMjAwMCwgMTAwMDAsIDMwMDAwLCBudWxsXTtcclxuLyoqIEBwcml2YXRlICovXHJcbmV4cG9ydCBjbGFzcyBEZWZhdWx0UmVjb25uZWN0UG9saWN5IHtcclxuICAgIGNvbnN0cnVjdG9yKHJldHJ5RGVsYXlzKSB7XHJcbiAgICAgICAgdGhpcy5fcmV0cnlEZWxheXMgPSByZXRyeURlbGF5cyAhPT0gdW5kZWZpbmVkID8gWy4uLnJldHJ5RGVsYXlzLCBudWxsXSA6IERFRkFVTFRfUkVUUllfREVMQVlTX0lOX01JTExJU0VDT05EUztcclxuICAgIH1cclxuICAgIG5leHRSZXRyeURlbGF5SW5NaWxsaXNlY29uZHMocmV0cnlDb250ZXh0KSB7XHJcbiAgICAgICAgcmV0dXJuIHRoaXMuX3JldHJ5RGVsYXlzW3JldHJ5Q29udGV4dC5wcmV2aW91c1JldHJ5Q291bnRdO1xyXG4gICAgfVxyXG59XHJcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPURlZmF1bHRSZWNvbm5lY3RQb2xpY3kuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js":
/*!************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/Errors.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortError: () => (/* binding */ AbortError),\n/* harmony export */   AggregateErrors: () => (/* binding */ AggregateErrors),\n/* harmony export */   DisabledTransportError: () => (/* binding */ DisabledTransportError),\n/* harmony export */   FailedToNegotiateWithServerError: () => (/* binding */ FailedToNegotiateWithServerError),\n/* harmony export */   FailedToStartTransportError: () => (/* binding */ FailedToStartTransportError),\n/* harmony export */   HttpError: () => (/* binding */ HttpError),\n/* harmony export */   TimeoutError: () => (/* binding */ TimeoutError),\n/* harmony export */   UnsupportedTransportError: () => (/* binding */ UnsupportedTransportError)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Error thrown when an HTTP request fails. */\r\nclass HttpError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage, statusCode) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when a timeout elapses. */\r\nclass TimeoutError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when an action is aborted. */\r\nclass AbortError extends Error {\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nclass UnsupportedTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nclass DisabledTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nclass FailedToStartTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nclass FailedToNegotiateWithServerError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nclass AggregateErrors extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message, innerErrors) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.innerErrors = innerErrors;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n//# sourceMappingURL=Errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FetchHttpClient: () => (/* binding */ FetchHttpClient)\n/* harmony export */ });\n/* harmony import */ var _Errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Errors */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\");\n/* harmony import */ var _HttpClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpClient.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\nclass FetchHttpClient extends _HttpClient__WEBPACK_IMPORTED_MODULE_0__.HttpClient {\r\n    constructor(logger) {\r\n        super();\r\n        this._logger = logger;\r\n        // Node added a fetch implementation to the global scope starting in v18.\r\n        // We need to add a cookie jar in node to be able to share cookies with WebSocket\r\n        if (typeof fetch === \"undefined\" || _Utils__WEBPACK_IMPORTED_MODULE_1__.Platform.isNode) {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc =  true ? require : 0;\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            if (typeof fetch === \"undefined\") {\r\n                this._fetchType = requireFunc(\"node-fetch\");\r\n            }\r\n            else {\r\n                // Use fetch from Node if available\r\n                this._fetchType = fetch;\r\n            }\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        }\r\n        else {\r\n            this._fetchType = fetch.bind((0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getGlobalThis)());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc =  true ? require : 0;\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        }\r\n        else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n    /** @inheritDoc */\r\n    async send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new _Errors__WEBPACK_IMPORTED_MODULE_2__.AbortError();\r\n        }\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n        const abortController = new this._abortControllerType();\r\n        let error;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new _Errors__WEBPACK_IMPORTED_MODULE_2__.AbortError();\r\n            };\r\n        }\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new _Errors__WEBPACK_IMPORTED_MODULE_2__.TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n        if (request.content === \"\") {\r\n            request.content = undefined;\r\n        }\r\n        if (request.content) {\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            request.headers = request.headers || {};\r\n            if ((0,_Utils__WEBPACK_IMPORTED_MODULE_1__.isArrayBuffer)(request.content)) {\r\n                request.headers[\"Content-Type\"] = \"application/octet-stream\";\r\n            }\r\n            else {\r\n                request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\r\n            }\r\n        }\r\n        let response;\r\n        try {\r\n            response = await this._fetchType(request.url, {\r\n                body: request.content,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        }\r\n        catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Warning, `Error from HTTP request. ${e}.`);\r\n            throw e;\r\n        }\r\n        finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\");\r\n            throw new _Errors__WEBPACK_IMPORTED_MODULE_2__.HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n        return new _HttpClient__WEBPACK_IMPORTED_MODULE_0__.HttpResponse(response.status, response.statusText, payload);\r\n    }\r\n    getCookieString(url) {\r\n        let cookies = \"\";\r\n        if (_Utils__WEBPACK_IMPORTED_MODULE_1__.Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\nfunction deserializeContent(response, responseType) {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n    return content;\r\n}\r\n//# sourceMappingURL=FetchHttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HandshakeProtocol: () => (/* binding */ HandshakeProtocol)\n/* harmony export */ });\n/* harmony import */ var _TextMessageFormat__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextMessageFormat */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n/** @private */\r\nclass HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    writeHandshakeRequest(handshakeRequest) {\r\n        return _TextMessageFormat__WEBPACK_IMPORTED_MODULE_0__.TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n    parseHandshakeResponse(data) {\r\n        let messageData;\r\n        let remainingData;\r\n        if ((0,_Utils__WEBPACK_IMPORTED_MODULE_1__.isArrayBuffer)(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(_TextMessageFormat__WEBPACK_IMPORTED_MODULE_0__.TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        }\r\n        else {\r\n            const textData = data;\r\n            const separatorIndex = textData.indexOf(_TextMessageFormat__WEBPACK_IMPORTED_MODULE_0__.TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n        // At this point we should have just the single handshake message\r\n        const messages = _TextMessageFormat__WEBPACK_IMPORTED_MODULE_0__.TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage = response;\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n//# sourceMappingURL=HandshakeProtocol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/HeaderNames.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/HeaderNames.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeaderNames: () => (/* binding */ HeaderNames)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nclass HeaderNames {\r\n}\r\nHeaderNames.Authorization = \"Authorization\";\r\nHeaderNames.Cookie = \"Cookie\";\r\n//# sourceMappingURL=HeaderNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC9zaWduYWxyL2Rpc3QvZXNtL0hlYWRlck5hbWVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXENhcHN0b25lUHJvamVjdFxcT3BlbkF1dG9tYXRlLkZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBtaWNyb3NvZnRcXHNpZ25hbHJcXGRpc3RcXGVzbVxcSGVhZGVyTmFtZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTGljZW5zZWQgdG8gdGhlIC5ORVQgRm91bmRhdGlvbiB1bmRlciBvbmUgb3IgbW9yZSBhZ3JlZW1lbnRzLlxyXG4vLyBUaGUgLk5FVCBGb3VuZGF0aW9uIGxpY2Vuc2VzIHRoaXMgZmlsZSB0byB5b3UgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxyXG5leHBvcnQgY2xhc3MgSGVhZGVyTmFtZXMge1xyXG59XHJcbkhlYWRlck5hbWVzLkF1dGhvcml6YXRpb24gPSBcIkF1dGhvcml6YXRpb25cIjtcclxuSGVhZGVyTmFtZXMuQ29va2llID0gXCJDb29raWVcIjtcclxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9SGVhZGVyTmFtZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/HeaderNames.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpClient.js":
/*!****************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/HttpClient.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpClient: () => (/* binding */ HttpClient),\n/* harmony export */   HttpResponse: () => (/* binding */ HttpResponse)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Represents an HTTP response. */\r\nclass HttpResponse {\r\n    constructor(statusCode, statusText, content) {\r\n        this.statusCode = statusCode;\r\n        this.statusText = statusText;\r\n        this.content = content;\r\n    }\r\n}\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nclass HttpClient {\r\n    get(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n    post(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n    delete(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    getCookieString(url) {\r\n        return \"\";\r\n    }\r\n}\r\n//# sourceMappingURL=HttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpConnection.js":
/*!********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/HttpConnection.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpConnection: () => (/* binding */ HttpConnection),\n/* harmony export */   TransportSendQueue: () => (/* binding */ TransportSendQueue)\n/* harmony export */ });\n/* harmony import */ var _AccessTokenHttpClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AccessTokenHttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js\");\n/* harmony import */ var _DefaultHttpClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DefaultHttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/DefaultHttpClient.js\");\n/* harmony import */ var _Errors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Errors */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _ITransport__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ITransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js\");\n/* harmony import */ var _LongPollingTransport__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LongPollingTransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js\");\n/* harmony import */ var _ServerSentEventsTransport__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ServerSentEventsTransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n/* harmony import */ var _WebSocketTransport__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./WebSocketTransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst MAX_REDIRECTS = 100;\r\n/** @private */\r\nclass HttpConnection {\r\n    constructor(url, options = {}) {\r\n        this._stopPromiseResolver = () => { };\r\n        this.features = {};\r\n        this._negotiateVersion = 1;\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isRequired(url, \"url\");\r\n        this._logger = (0,_Utils__WEBPACK_IMPORTED_MODULE_0__.createLogger)(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        }\r\n        else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n        let webSocketModule = null;\r\n        let eventSourceModule = null;\r\n        if (_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isNode && \"function\" !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc =  true ? require : 0;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n        if (!_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        }\r\n        else if (_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n        if (!_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        }\r\n        else if (_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n        this._httpClient = new _AccessTokenHttpClient__WEBPACK_IMPORTED_MODULE_1__.AccessTokenHttpClient(options.httpClient || new _DefaultHttpClient__WEBPACK_IMPORTED_MODULE_2__.DefaultHttpClient(this._logger), options.accessTokenFactory);\r\n        this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async start(transferFormat) {\r\n        transferFormat = transferFormat || _ITransport__WEBPACK_IMPORTED_MODULE_3__.TransferFormat.Binary;\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isIn(transferFormat, _ITransport__WEBPACK_IMPORTED_MODULE_3__.TransferFormat, \"transferFormat\");\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Starting connection with transfer format '${_ITransport__WEBPACK_IMPORTED_MODULE_3__.TransferFormat[transferFormat]}'.`);\r\n        if (this._connectionState !== \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this._connectionState = \"Connecting\" /* ConnectionState.Connecting */;\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, message);\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n            return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_5__.AbortError(message));\r\n        }\r\n        else if (this._connectionState !== \"Connected\" /* ConnectionState.Connected */) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, message);\r\n            return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_5__.AbortError(message));\r\n        }\r\n        this._connectionStarted = true;\r\n    }\r\n    send(data) {\r\n        if (this._connectionState !== \"Connected\" /* ConnectionState.Connected */) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport);\r\n        }\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n    async stop(error) {\r\n        if (this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n        if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n        this._connectionState = \"Disconnecting\" /* ConnectionState.Disconnecting */;\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n    async _stopInternal(error) {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n        try {\r\n            await this._startInternalPromise;\r\n        }\r\n        catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            }\r\n            catch (e) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n            this.transport = undefined;\r\n        }\r\n        else {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n    async _startInternal(transferFormat) {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n        this._httpClient._accessTokenFactory = this._accessTokenFactory;\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                }\r\n                else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            }\r\n            else {\r\n                let negotiateResponse = null;\r\n                let redirects = 0;\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */ || this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n                        throw new _Errors__WEBPACK_IMPORTED_MODULE_5__.AbortError(\"The connection was stopped during negotiation.\");\r\n                    }\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n                    if (negotiateResponse.ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                        // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\r\n                        this._httpClient._accessToken = accessToken;\r\n                        this._httpClient._accessTokenFactory = undefined;\r\n                    }\r\n                    redirects++;\r\n                } while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n            if (this.transport instanceof _LongPollingTransport__WEBPACK_IMPORTED_MODULE_6__.LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n            if (this._connectionState === \"Connecting\" /* ConnectionState.Connecting */) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = \"Connected\" /* ConnectionState.Connected */;\r\n            }\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        }\r\n        catch (e) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\r\n            this.transport = undefined;\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n    async _getNegotiationResponse(url) {\r\n        const headers = {};\r\n        const [name, value] = (0,_Utils__WEBPACK_IMPORTED_MODULE_0__.getUserAgentHeader)();\r\n        headers[name] = value;\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n            const negotiateResponse = JSON.parse(response.content);\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            if (negotiateResponse.useStatefulReconnect && this._options._useStatefulReconnect !== true) {\r\n                return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_5__.FailedToNegotiateWithServerError(\"Client didn't negotiate Stateful Reconnect but the server did.\"));\r\n            }\r\n            return negotiateResponse;\r\n        }\r\n        catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof _Errors__WEBPACK_IMPORTED_MODULE_5__.HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, errorMessage);\r\n            return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_5__.FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n    _createConnectUrl(url, connectionToken) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n    async _createTransport(url, requestedTransport, negotiateResponse, requestedTransferFormat) {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n        const transportExceptions = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat, (negotiate === null || negotiate === void 0 ? void 0 : negotiate.useStatefulReconnect) === true);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            }\r\n            else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    }\r\n                    catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                }\r\n                catch (ex) {\r\n                    this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new _Errors__WEBPACK_IMPORTED_MODULE_5__.FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[endpoint.transport]));\r\n                    if (this._connectionState !== \"Connecting\" /* ConnectionState.Connecting */) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, message);\r\n                        return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_5__.AbortError(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_5__.AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n    _constructTransport(transport) {\r\n        switch (transport) {\r\n            case _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new _WebSocketTransport__WEBPACK_IMPORTED_MODULE_7__.WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent, this._options.WebSocket, this._options.headers || {});\r\n            case _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new _ServerSentEventsTransport__WEBPACK_IMPORTED_MODULE_8__.ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\r\n            case _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.LongPolling:\r\n                return new _LongPollingTransport__WEBPACK_IMPORTED_MODULE_6__.LongPollingTransport(this._httpClient, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n    _startTransport(url, transferFormat) {\r\n        this.transport.onreceive = this.onreceive;\r\n        if (this.features.reconnect) {\r\n            this.transport.onclose = async (e) => {\r\n                let callStop = false;\r\n                if (this.features.reconnect) {\r\n                    try {\r\n                        this.features.disconnected();\r\n                        await this.transport.connect(url, transferFormat);\r\n                        await this.features.resend();\r\n                    }\r\n                    catch {\r\n                        callStop = true;\r\n                    }\r\n                }\r\n                else {\r\n                    this._stopConnection(e);\r\n                    return;\r\n                }\r\n                if (callStop) {\r\n                    this._stopConnection(e);\r\n                }\r\n            };\r\n        }\r\n        else {\r\n            this.transport.onclose = (e) => this._stopConnection(e);\r\n        }\r\n        return this.transport.connect(url, transferFormat);\r\n    }\r\n    _resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat, useStatefulReconnect) {\r\n        const transport = _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        }\r\n        else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => _ITransport__WEBPACK_IMPORTED_MODULE_3__.TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Skipping transport '${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new _Errors__WEBPACK_IMPORTED_MODULE_5__.UnsupportedTransportError(`'${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    }\r\n                    else {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Selecting transport '${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            this.features.reconnect = transport === _ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType.WebSockets ? useStatefulReconnect : undefined;\r\n                            return this._constructTransport(transport);\r\n                        }\r\n                        catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                }\r\n                else {\r\n                    this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Skipping transport '${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}' because it does not support the requested transfer format '${_ITransport__WEBPACK_IMPORTED_MODULE_3__.TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}' does not support ${_ITransport__WEBPACK_IMPORTED_MODULE_3__.TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            }\r\n            else {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Skipping transport '${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new _Errors__WEBPACK_IMPORTED_MODULE_5__.DisabledTransportError(`'${_ITransport__WEBPACK_IMPORTED_MODULE_3__.HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n    _isITransport(transport) {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n    _stopConnection(error) {\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n        this.transport = undefined;\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n        if (this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n        if (this._connectionState === \"Connecting\" /* ConnectionState.Connecting */) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n        if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n        if (error) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        }\r\n        else {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n        this.connectionId = undefined;\r\n        this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            }\r\n            catch (e) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n    _resolveUrl(url) {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n        if (!_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n    _resolveNegotiateUrl(url) {\r\n        const negotiateUrl = new URL(url);\r\n        if (negotiateUrl.pathname.endsWith('/')) {\r\n            negotiateUrl.pathname += \"negotiate\";\r\n        }\r\n        else {\r\n            negotiateUrl.pathname += \"/negotiate\";\r\n        }\r\n        const searchParams = new URLSearchParams(negotiateUrl.searchParams);\r\n        if (!searchParams.has(\"negotiateVersion\")) {\r\n            searchParams.append(\"negotiateVersion\", this._negotiateVersion.toString());\r\n        }\r\n        if (searchParams.has(\"useStatefulReconnect\")) {\r\n            if (searchParams.get(\"useStatefulReconnect\") === \"true\") {\r\n                this._options._useStatefulReconnect = true;\r\n            }\r\n        }\r\n        else if (this._options._useStatefulReconnect === true) {\r\n            searchParams.append(\"useStatefulReconnect\", \"true\");\r\n        }\r\n        negotiateUrl.search = searchParams.toString();\r\n        return negotiateUrl.toString();\r\n    }\r\n}\r\nfunction transportMatches(requestedTransport, actualTransport) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n/** @private */\r\nclass TransportSendQueue {\r\n    constructor(_transport) {\r\n        this._transport = _transport;\r\n        this._buffer = [];\r\n        this._executing = true;\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n    send(data) {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n    stop() {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n    _bufferData(data) {\r\n        if (this._buffer.length && typeof (this._buffer[0]) !== typeof (data)) {\r\n            throw new Error(`Expected data to be of type ${typeof (this._buffer)} but was of type ${typeof (data)}`);\r\n        }\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n    async _sendLoop() {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n                break;\r\n            }\r\n            this._sendBufferedData = new PromiseSource();\r\n            const transportResult = this._transportResult;\r\n            this._transportResult = undefined;\r\n            const data = typeof (this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n            this._buffer.length = 0;\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            }\r\n            catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n    static _concatBuffers(arrayBuffers) {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n        return result.buffer;\r\n    }\r\n}\r\nclass PromiseSource {\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n    resolve() {\r\n        this._resolver();\r\n    }\r\n    reject(reason) {\r\n        this._rejecter(reason);\r\n    }\r\n}\r\n//# sourceMappingURL=HttpConnection.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpConnection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/HubConnection.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/HubConnection.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HubConnection: () => (/* binding */ HubConnection),\n/* harmony export */   HubConnectionState: () => (/* binding */ HubConnectionState)\n/* harmony export */ });\n/* harmony import */ var _HandshakeProtocol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HandshakeProtocol */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js\");\n/* harmony import */ var _Errors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Errors */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\");\n/* harmony import */ var _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./IHubProtocol */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _Subject__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Subject */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Subject.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n/* harmony import */ var _MessageBuffer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MessageBuffer */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS = 15 * 1000;\r\nconst DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE = 100000;\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nvar HubConnectionState;\r\n(function (HubConnectionState) {\r\n    /** The hub connection is disconnected. */\r\n    HubConnectionState[\"Disconnected\"] = \"Disconnected\";\r\n    /** The hub connection is connecting. */\r\n    HubConnectionState[\"Connecting\"] = \"Connecting\";\r\n    /** The hub connection is connected. */\r\n    HubConnectionState[\"Connected\"] = \"Connected\";\r\n    /** The hub connection is disconnecting. */\r\n    HubConnectionState[\"Disconnecting\"] = \"Disconnecting\";\r\n    /** The hub connection is reconnecting. */\r\n    HubConnectionState[\"Reconnecting\"] = \"Reconnecting\";\r\n})(HubConnectionState || (HubConnectionState = {}));\r\n/** Represents a connection to a SignalR Hub. */\r\nclass HubConnection {\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    static create(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize);\r\n    }\r\n    constructor(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\r\n        this._nextKeepAlive = 0;\r\n        this._freezeEventListener = () => {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n        };\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(connection, \"connection\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(logger, \"logger\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(protocol, \"protocol\");\r\n        this.serverTimeoutInMilliseconds = serverTimeoutInMilliseconds !== null && serverTimeoutInMilliseconds !== void 0 ? serverTimeoutInMilliseconds : DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = keepAliveIntervalInMilliseconds !== null && keepAliveIntervalInMilliseconds !== void 0 ? keepAliveIntervalInMilliseconds : DEFAULT_PING_INTERVAL_IN_MS;\r\n        this._statefulReconnectBufferSize = statefulReconnectBufferSize !== null && statefulReconnectBufferSize !== void 0 ? statefulReconnectBufferSize : DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE;\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new _HandshakeProtocol__WEBPACK_IMPORTED_MODULE_2__.HandshakeProtocol();\r\n        this.connection.onreceive = (data) => this._processIncomingData(data);\r\n        this.connection.onclose = (error) => this._connectionClosed(error);\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Ping });\r\n    }\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state() {\r\n        return this._connectionState;\r\n    }\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId() {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl() {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n        this.connection.baseUrl = url;\r\n    }\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    start() {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n    async _startWithStateTransitions() {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Starting HubConnection.\");\r\n        try {\r\n            await this._startInternal();\r\n            if (_Utils__WEBPACK_IMPORTED_MODULE_1__.Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        }\r\n        catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n    async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n        await this.connection.start(this._protocol.transferFormat);\r\n        try {\r\n            let version = this._protocol.version;\r\n            if (!this.connection.features.reconnect) {\r\n                // Stateful Reconnect starts with HubProtocol version 2, newer clients connecting to older servers will fail to connect due to\r\n                // the handshake only supporting version 1, so we will try to send version 1 during the handshake to keep old servers working.\r\n                version = 1;\r\n            }\r\n            const handshakeRequest = {\r\n                protocol: this._protocol.name,\r\n                version,\r\n            };\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Sending handshake request.\");\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n            await handshakePromise;\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n            const useStatefulReconnect = this.connection.features.reconnect || false;\r\n            if (useStatefulReconnect) {\r\n                this._messageBuffer = new _MessageBuffer__WEBPACK_IMPORTED_MODULE_4__.MessageBuffer(this._protocol, this.connection, this._statefulReconnectBufferSize);\r\n                this.connection.features.disconnected = this._messageBuffer._disconnected.bind(this._messageBuffer);\r\n                this.connection.features.resend = () => {\r\n                    if (this._messageBuffer) {\r\n                        return this._messageBuffer._resend();\r\n                    }\r\n                };\r\n            }\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        }\r\n        catch (e) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    async stop() {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n        this.connection.features.reconnect = false;\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        }\r\n        catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n    _stopInternal(error) {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n        const state = this._connectionState;\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Stopping HubConnection.\");\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n        if (state === HubConnectionState.Connected) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._sendCloseMessage();\r\n        }\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new _Errors__WEBPACK_IMPORTED_MODULE_5__.AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n    async _sendCloseMessage() {\r\n        try {\r\n            await this._sendWithProtocol(this._createCloseMessage());\r\n        }\r\n        catch {\r\n            // Ignore, this is a best effort attempt to let the server know the client closed gracefully.\r\n        }\r\n    }\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    stream(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue;\r\n        const subject = new _Subject__WEBPACK_IMPORTED_MODULE_6__.Subject();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            }\r\n            else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    }\r\n                    else {\r\n                        subject.complete();\r\n                    }\r\n                }\r\n                else {\r\n                    subject.next((invocationEvent.item));\r\n                }\r\n            }\r\n        };\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n            subject.error(e);\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n        });\r\n        this._launchStreams(streams, promiseQueue);\r\n        return subject;\r\n    }\r\n    _sendMessage(message) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    _sendWithProtocol(message) {\r\n        if (this._messageBuffer) {\r\n            return this._messageBuffer._send(message);\r\n        }\r\n        else {\r\n            return this._sendMessage(this._protocol.writeMessage(message));\r\n        }\r\n    }\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    send(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n        this._launchStreams(streams, sendPromise);\r\n        return sendPromise;\r\n    }\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    invoke(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n        const p = new Promise((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                }\r\n                else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        }\r\n                        else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    }\r\n                    else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                reject(e);\r\n                // invocationId will always have a value for a non-blocking invocation\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n        return p;\r\n    }\r\n    on(methodName, newMethod) {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n    off(methodName, method) {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            delete this._methods[methodName];\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    onclose(callback) {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    onreconnecting(callback) {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    onreconnected(callback) {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n    _processIncomingData(data) {\r\n        this._cleanupTimeout();\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n            for (const message of messages) {\r\n                if (this._messageBuffer && !this._messageBuffer._shouldProcessMessage(message)) {\r\n                    // Don't process the message, we are either waiting for a SequenceMessage or received a duplicate message\r\n                    continue;\r\n                }\r\n                switch (message.type) {\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Invocation:\r\n                        this._invokeClientMethod(message)\r\n                            .catch((e) => {\r\n                            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `Invoke client method threw error: ${(0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getErrorString)(e)}`);\r\n                        });\r\n                        break;\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.StreamItem:\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            }\r\n                            catch (e) {\r\n                                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `Stream callback threw error: ${(0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getErrorString)(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Close: {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, \"Close message received from server.\");\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        }\r\n                        else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Ack:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._ack(message);\r\n                        }\r\n                        break;\r\n                    case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Sequence:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._resetSequence(message);\r\n                        }\r\n                        break;\r\n                    default:\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n        this._resetTimeoutPeriod();\r\n    }\r\n    _processHandshakeResponse(data) {\r\n        let responseMessage;\r\n        let remainingData;\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        }\r\n        catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, message);\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, message);\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        else {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n    _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n        this._cleanupPingTimer();\r\n    }\r\n    _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined) {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        }\r\n                        catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n    async _invokeClientMethod(invocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            }\r\n            catch (e) {\r\n                exception = e;\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        }\r\n        else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `${exception}`, null);\r\n            }\r\n            else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, null, res);\r\n            }\r\n            else {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        }\r\n        else {\r\n            if (res) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n    _connectionClosed(error) {\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new _Errors__WEBPACK_IMPORTED_MODULE_5__.AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        }\r\n        else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        }\r\n        else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n    _completeClose(error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n            if (this._messageBuffer) {\r\n                this._messageBuffer._dispose(error !== null && error !== void 0 ? error : new Error(\"Connection closed.\"));\r\n                this._messageBuffer = undefined;\r\n            }\r\n            if (_Utils__WEBPACK_IMPORTED_MODULE_1__.Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            }\r\n            catch (e) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n    async _reconnect(error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n        if (error) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        }\r\n        else {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            }\r\n            catch (e) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n            try {\r\n                await this._startInternal();\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    }\r\n                    catch (e) {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n                return;\r\n            }\r\n            catch (e) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n        this._completeClose();\r\n    }\r\n    _getNextRetryDelay(previousRetryCount, elapsedMilliseconds, retryReason) {\r\n        try {\r\n            return this._reconnectPolicy.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        }\r\n        catch (e) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n    _cancelCallbacksWithError(error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n            const callback = callbacks[key];\r\n            try {\r\n                callback(null, error);\r\n            }\r\n            catch (e) {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${(0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getErrorString)(e)}`);\r\n            }\r\n        });\r\n    }\r\n    _cleanupPingTimer() {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n    _cleanupTimeout() {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n    _createInvocation(methodName, args, nonblocking, streamIds) {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Invocation,\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n        else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Invocation,\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n    _launchStreams(streams, promiseQueue) {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    }\r\n                    else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    }\r\n                    else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n    _replaceStreamingParams(args) {\r\n        const streams = [];\r\n        const streamIds = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n        return [streams, streamIds];\r\n    }\r\n    _isObservable(arg) {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n    _createStreamInvocation(methodName, args, streamIds) {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.StreamInvocation,\r\n            };\r\n        }\r\n        else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n    _createCancelInvocation(id) {\r\n        return {\r\n            invocationId: id,\r\n            type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.CancelInvocation,\r\n        };\r\n    }\r\n    _createStreamItemMessage(id, item) {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.StreamItem,\r\n        };\r\n    }\r\n    _createCompletionMessage(id, error, result) {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion,\r\n            };\r\n        }\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion,\r\n        };\r\n    }\r\n    _createCloseMessage() {\r\n        return { type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Close };\r\n    }\r\n}\r\n//# sourceMappingURL=HubConnection.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/HubConnection.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/HubConnectionBuilder.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/HubConnectionBuilder.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HubConnectionBuilder: () => (/* binding */ HubConnectionBuilder)\n/* harmony export */ });\n/* harmony import */ var _DefaultReconnectPolicy__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DefaultReconnectPolicy */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js\");\n/* harmony import */ var _HttpConnection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HttpConnection */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpConnection.js\");\n/* harmony import */ var _HubConnection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./HubConnection */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HubConnection.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _JsonHubProtocol__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./JsonHubProtocol */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js\");\n/* harmony import */ var _Loggers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Loggers */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Loggers.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst LogLevelNameMapping = {\r\n    trace: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Trace,\r\n    debug: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Debug,\r\n    info: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information,\r\n    information: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information,\r\n    warn: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning,\r\n    warning: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning,\r\n    error: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error,\r\n    critical: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Critical,\r\n    none: _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.None,\r\n};\r\nfunction parseLogLevel(name) {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    }\r\n    else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nclass HubConnectionBuilder {\r\n    configureLogging(logging) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(logging, \"logging\");\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        }\r\n        else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new _Utils__WEBPACK_IMPORTED_MODULE_1__.ConsoleLogger(logLevel);\r\n        }\r\n        else {\r\n            this.logger = new _Utils__WEBPACK_IMPORTED_MODULE_1__.ConsoleLogger(logging);\r\n        }\r\n        return this;\r\n    }\r\n    withUrl(url, transportTypeOrOptions) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(url, \"url\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isNotEmpty(url, \"url\");\r\n        this.url = url;\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        }\r\n        else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n        return this;\r\n    }\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    withHubProtocol(protocol) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(protocol, \"protocol\");\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n    withAutomaticReconnect(retryDelaysOrReconnectPolicy) {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new _DefaultReconnectPolicy__WEBPACK_IMPORTED_MODULE_2__.DefaultReconnectPolicy();\r\n        }\r\n        else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new _DefaultReconnectPolicy__WEBPACK_IMPORTED_MODULE_2__.DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        }\r\n        else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n        return this;\r\n    }\r\n    /** Configures {@link @microsoft/signalr.HubConnection.serverTimeoutInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    withServerTimeout(milliseconds) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(milliseconds, \"milliseconds\");\r\n        this._serverTimeoutInMilliseconds = milliseconds;\r\n        return this;\r\n    }\r\n    /** Configures {@link @microsoft/signalr.HubConnection.keepAliveIntervalInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    withKeepAliveInterval(milliseconds) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(milliseconds, \"milliseconds\");\r\n        this._keepAliveIntervalInMilliseconds = milliseconds;\r\n        return this;\r\n    }\r\n    /** Enables and configures options for the Stateful Reconnect feature.\r\n     *\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    withStatefulReconnect(options) {\r\n        if (this.httpConnectionOptions === undefined) {\r\n            this.httpConnectionOptions = {};\r\n        }\r\n        this.httpConnectionOptions._useStatefulReconnect = true;\r\n        this._statefulReconnectBufferSize = options === null || options === void 0 ? void 0 : options.bufferSize;\r\n        return this;\r\n    }\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    build() {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new _HttpConnection__WEBPACK_IMPORTED_MODULE_3__.HttpConnection(this.url, httpConnectionOptions);\r\n        return _HubConnection__WEBPACK_IMPORTED_MODULE_4__.HubConnection.create(connection, this.logger || _Loggers__WEBPACK_IMPORTED_MODULE_5__.NullLogger.instance, this.protocol || new _JsonHubProtocol__WEBPACK_IMPORTED_MODULE_6__.JsonHubProtocol(), this.reconnectPolicy, this._serverTimeoutInMilliseconds, this._keepAliveIntervalInMilliseconds, this._statefulReconnectBufferSize);\r\n    }\r\n}\r\nfunction isLogger(logger) {\r\n    return logger.log !== undefined;\r\n}\r\n//# sourceMappingURL=HubConnectionBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/HubConnectionBuilder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js":
/*!******************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageType: () => (/* binding */ MessageType)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Defines the type of a Hub Message. */\r\nvar MessageType;\r\n(function (MessageType) {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    MessageType[MessageType[\"Invocation\"] = 1] = \"Invocation\";\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    MessageType[MessageType[\"StreamItem\"] = 2] = \"StreamItem\";\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    MessageType[MessageType[\"Completion\"] = 3] = \"Completion\";\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    MessageType[MessageType[\"StreamInvocation\"] = 4] = \"StreamInvocation\";\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    MessageType[MessageType[\"CancelInvocation\"] = 5] = \"CancelInvocation\";\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    MessageType[MessageType[\"Ping\"] = 6] = \"Ping\";\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    MessageType[MessageType[\"Close\"] = 7] = \"Close\";\r\n    MessageType[MessageType[\"Ack\"] = 8] = \"Ack\";\r\n    MessageType[MessageType[\"Sequence\"] = 9] = \"Sequence\";\r\n})(MessageType || (MessageType = {}));\r\n//# sourceMappingURL=IHubProtocol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js":
/*!*************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/ILogger.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogLevel: () => (/* binding */ LogLevel)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nvar LogLevel;\r\n(function (LogLevel) {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Trace\"] = 0] = \"Trace\";\r\n    /** Log level for low severity diagnostic messages. */\r\n    LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\r\n    /** Log level for informational diagnostic messages. */\r\n    LogLevel[LogLevel[\"Information\"] = 2] = \"Information\";\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    LogLevel[LogLevel[\"Warning\"] = 3] = \"Warning\";\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    LogLevel[LogLevel[\"Critical\"] = 5] = \"Critical\";\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    LogLevel[LogLevel[\"None\"] = 6] = \"None\";\r\n})(LogLevel || (LogLevel = {}));\r\n//# sourceMappingURL=ILogger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js":
/*!****************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/ITransport.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HttpTransportType: () => (/* binding */ HttpTransportType),\n/* harmony export */   TransferFormat: () => (/* binding */ TransferFormat)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nvar HttpTransportType;\r\n(function (HttpTransportType) {\r\n    /** Specifies no transport preference. */\r\n    HttpTransportType[HttpTransportType[\"None\"] = 0] = \"None\";\r\n    /** Specifies the WebSockets transport. */\r\n    HttpTransportType[HttpTransportType[\"WebSockets\"] = 1] = \"WebSockets\";\r\n    /** Specifies the Server-Sent Events transport. */\r\n    HttpTransportType[HttpTransportType[\"ServerSentEvents\"] = 2] = \"ServerSentEvents\";\r\n    /** Specifies the Long Polling transport. */\r\n    HttpTransportType[HttpTransportType[\"LongPolling\"] = 4] = \"LongPolling\";\r\n})(HttpTransportType || (HttpTransportType = {}));\r\n/** Specifies the transfer format for a connection. */\r\nvar TransferFormat;\r\n(function (TransferFormat) {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Text\"] = 1] = \"Text\";\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Binary\"] = 2] = \"Binary\";\r\n})(TransferFormat || (TransferFormat = {}));\r\n//# sourceMappingURL=ITransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC9zaWduYWxyL2Rpc3QvZXNtL0lUcmFuc3BvcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4Q0FBOEM7QUFDL0M7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHdDQUF3QztBQUN6QyIsInNvdXJjZXMiOlsiRDpcXENhcHN0b25lUHJvamVjdFxcT3BlbkF1dG9tYXRlLkZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBtaWNyb3NvZnRcXHNpZ25hbHJcXGRpc3RcXGVzbVxcSVRyYW5zcG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMaWNlbnNlZCB0byB0aGUgLk5FVCBGb3VuZGF0aW9uIHVuZGVyIG9uZSBvciBtb3JlIGFncmVlbWVudHMuXHJcbi8vIFRoZSAuTkVUIEZvdW5kYXRpb24gbGljZW5zZXMgdGhpcyBmaWxlIHRvIHlvdSB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXHJcbi8vIFRoaXMgd2lsbCBiZSB0cmVhdGVkIGFzIGEgYml0IGZsYWcgaW4gdGhlIGZ1dHVyZSwgc28gd2Uga2VlcCBpdCB1c2luZyBwb3dlci1vZi10d28gdmFsdWVzLlxyXG4vKiogU3BlY2lmaWVzIGEgc3BlY2lmaWMgSFRUUCB0cmFuc3BvcnQgdHlwZS4gKi9cclxuZXhwb3J0IHZhciBIdHRwVHJhbnNwb3J0VHlwZTtcclxuKGZ1bmN0aW9uIChIdHRwVHJhbnNwb3J0VHlwZSkge1xyXG4gICAgLyoqIFNwZWNpZmllcyBubyB0cmFuc3BvcnQgcHJlZmVyZW5jZS4gKi9cclxuICAgIEh0dHBUcmFuc3BvcnRUeXBlW0h0dHBUcmFuc3BvcnRUeXBlW1wiTm9uZVwiXSA9IDBdID0gXCJOb25lXCI7XHJcbiAgICAvKiogU3BlY2lmaWVzIHRoZSBXZWJTb2NrZXRzIHRyYW5zcG9ydC4gKi9cclxuICAgIEh0dHBUcmFuc3BvcnRUeXBlW0h0dHBUcmFuc3BvcnRUeXBlW1wiV2ViU29ja2V0c1wiXSA9IDFdID0gXCJXZWJTb2NrZXRzXCI7XHJcbiAgICAvKiogU3BlY2lmaWVzIHRoZSBTZXJ2ZXItU2VudCBFdmVudHMgdHJhbnNwb3J0LiAqL1xyXG4gICAgSHR0cFRyYW5zcG9ydFR5cGVbSHR0cFRyYW5zcG9ydFR5cGVbXCJTZXJ2ZXJTZW50RXZlbnRzXCJdID0gMl0gPSBcIlNlcnZlclNlbnRFdmVudHNcIjtcclxuICAgIC8qKiBTcGVjaWZpZXMgdGhlIExvbmcgUG9sbGluZyB0cmFuc3BvcnQuICovXHJcbiAgICBIdHRwVHJhbnNwb3J0VHlwZVtIdHRwVHJhbnNwb3J0VHlwZVtcIkxvbmdQb2xsaW5nXCJdID0gNF0gPSBcIkxvbmdQb2xsaW5nXCI7XHJcbn0pKEh0dHBUcmFuc3BvcnRUeXBlIHx8IChIdHRwVHJhbnNwb3J0VHlwZSA9IHt9KSk7XHJcbi8qKiBTcGVjaWZpZXMgdGhlIHRyYW5zZmVyIGZvcm1hdCBmb3IgYSBjb25uZWN0aW9uLiAqL1xyXG5leHBvcnQgdmFyIFRyYW5zZmVyRm9ybWF0O1xyXG4oZnVuY3Rpb24gKFRyYW5zZmVyRm9ybWF0KSB7XHJcbiAgICAvKiogU3BlY2lmaWVzIHRoYXQgb25seSB0ZXh0IGRhdGEgd2lsbCBiZSB0cmFuc21pdHRlZCBvdmVyIHRoZSBjb25uZWN0aW9uLiAqL1xyXG4gICAgVHJhbnNmZXJGb3JtYXRbVHJhbnNmZXJGb3JtYXRbXCJUZXh0XCJdID0gMV0gPSBcIlRleHRcIjtcclxuICAgIC8qKiBTcGVjaWZpZXMgdGhhdCBiaW5hcnkgZGF0YSB3aWxsIGJlIHRyYW5zbWl0dGVkIG92ZXIgdGhlIGNvbm5lY3Rpb24uICovXHJcbiAgICBUcmFuc2ZlckZvcm1hdFtUcmFuc2ZlckZvcm1hdFtcIkJpbmFyeVwiXSA9IDJdID0gXCJCaW5hcnlcIjtcclxufSkoVHJhbnNmZXJGb3JtYXQgfHwgKFRyYW5zZmVyRm9ybWF0ID0ge30pKTtcclxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9SVRyYW5zcG9ydC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonHubProtocol: () => (/* binding */ JsonHubProtocol)\n/* harmony export */ });\n/* harmony import */ var _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./IHubProtocol */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _ITransport__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ITransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js\");\n/* harmony import */ var _Loggers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Loggers */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Loggers.js\");\n/* harmony import */ var _TextMessageFormat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TextMessageFormat */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n\r\nconst JSON_HUB_PROTOCOL_NAME = \"json\";\r\n/** Implements the JSON Hub Protocol. */\r\nclass JsonHubProtocol {\r\n    constructor() {\r\n        /** @inheritDoc */\r\n        this.name = JSON_HUB_PROTOCOL_NAME;\r\n        /** @inheritDoc */\r\n        this.version = 2;\r\n        /** @inheritDoc */\r\n        this.transferFormat = _ITransport__WEBPACK_IMPORTED_MODULE_0__.TransferFormat.Text;\r\n    }\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input, logger) {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n        if (!input) {\r\n            return [];\r\n        }\r\n        if (logger === null) {\r\n            logger = _Loggers__WEBPACK_IMPORTED_MODULE_1__.NullLogger.instance;\r\n        }\r\n        // Parse the messages\r\n        const messages = _TextMessageFormat__WEBPACK_IMPORTED_MODULE_2__.TextMessageFormat.parse(input);\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message);\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Ack:\r\n                    this._isAckMessage(parsedMessage);\r\n                    break;\r\n                case _IHubProtocol__WEBPACK_IMPORTED_MODULE_3__.MessageType.Sequence:\r\n                    this._isSequenceMessage(parsedMessage);\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_4__.LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n        return hubMessages;\r\n    }\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message) {\r\n        return _TextMessageFormat__WEBPACK_IMPORTED_MODULE_2__.TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n    _isInvocationMessage(message) {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n    _isStreamItemMessage(message) {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n    _isCompletionMessage(message) {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n    _isAckMessage(message) {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Ack message.\");\r\n        }\r\n    }\r\n    _isSequenceMessage(message) {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Sequence message.\");\r\n        }\r\n    }\r\n    _assertNotEmptyString(value, errorMessage) {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n//# sourceMappingURL=JsonHubProtocol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/Loggers.js":
/*!*************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/Loggers.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NullLogger: () => (/* binding */ NullLogger)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** A logger that does nothing when log messages are sent to it. */\r\nclass NullLogger {\r\n    constructor() { }\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    log(_logLevel, _message) {\r\n    }\r\n}\r\n/** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\nNullLogger.instance = new NullLogger();\r\n//# sourceMappingURL=Loggers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC9zaWduYWxyL2Rpc3QvZXNtL0xvZ2dlcnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLG9DQUFvQztBQUN2RTtBQUNBIiwic291cmNlcyI6WyJEOlxcQ2Fwc3RvbmVQcm9qZWN0XFxPcGVuQXV0b21hdGUuRnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQG1pY3Jvc29mdFxcc2lnbmFsclxcZGlzdFxcZXNtXFxMb2dnZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIExpY2Vuc2VkIHRvIHRoZSAuTkVUIEZvdW5kYXRpb24gdW5kZXIgb25lIG9yIG1vcmUgYWdyZWVtZW50cy5cclxuLy8gVGhlIC5ORVQgRm91bmRhdGlvbiBsaWNlbnNlcyB0aGlzIGZpbGUgdG8geW91IHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cclxuLyoqIEEgbG9nZ2VyIHRoYXQgZG9lcyBub3RoaW5nIHdoZW4gbG9nIG1lc3NhZ2VzIGFyZSBzZW50IHRvIGl0LiAqL1xyXG5leHBvcnQgY2xhc3MgTnVsbExvZ2dlciB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHsgfVxyXG4gICAgLyoqIEBpbmhlcml0RG9jICovXHJcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmVcclxuICAgIGxvZyhfbG9nTGV2ZWwsIF9tZXNzYWdlKSB7XHJcbiAgICB9XHJcbn1cclxuLyoqIFRoZSBzaW5nbGV0b24gaW5zdGFuY2Ugb2YgdGhlIHtAbGluayBAbWljcm9zb2Z0L3NpZ25hbHIuTnVsbExvZ2dlcn0uICovXHJcbk51bGxMb2dnZXIuaW5zdGFuY2UgPSBuZXcgTnVsbExvZ2dlcigpO1xyXG4vLyMgc291cmNlTWFwcGluZ1VSTD1Mb2dnZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/Loggers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LongPollingTransport: () => (/* binding */ LongPollingTransport)\n/* harmony export */ });\n/* harmony import */ var _AbortController__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AbortController */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/AbortController.js\");\n/* harmony import */ var _Errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Errors */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _ITransport__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ITransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nclass LongPollingTransport {\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    get pollAborted() {\r\n        return this._pollAbort.aborted;\r\n    }\r\n    constructor(httpClient, logger, options) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new _AbortController__WEBPACK_IMPORTED_MODULE_0__.AbortController();\r\n        this._options = options;\r\n        this._running = false;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(url, \"url\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isRequired(transferFormat, \"transferFormat\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_1__.Arg.isIn(transferFormat, _ITransport__WEBPACK_IMPORTED_MODULE_2__.TransferFormat, \"transferFormat\");\r\n        this._url = url;\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === _ITransport__WEBPACK_IMPORTED_MODULE_2__.TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n        const [name, value] = (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getUserAgentHeader)();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n        const pollOptions = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n        if (transferFormat === _ITransport__WEBPACK_IMPORTED_MODULE_2__.TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new _Errors__WEBPACK_IMPORTED_MODULE_4__.HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        }\r\n        else {\r\n            this._running = true;\r\n        }\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n    async _poll(url, pollOptions) {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n                        this._running = false;\r\n                    }\r\n                    else if (response.statusCode !== 200) {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n                        // Unexpected status code\r\n                        this._closeError = new _Errors__WEBPACK_IMPORTED_MODULE_4__.HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    }\r\n                    else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, `(LongPolling transport) data received. ${(0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getDataDetail)(response.content, this._options.logMessageContent)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        }\r\n                        else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    }\r\n                    else {\r\n                        if (e instanceof _Errors__WEBPACK_IMPORTED_MODULE_4__.TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                        else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        finally {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n    async send(data) {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.sendMessage)(this._logger, \"LongPolling\", this._httpClient, this._url, data, this._options);\r\n    }\r\n    async stop() {\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n        try {\r\n            await this._receiving;\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n            const headers = {};\r\n            const [name, value] = (0,_Utils__WEBPACK_IMPORTED_MODULE_1__.getUserAgentHeader)();\r\n            headers[name] = value;\r\n            const deleteOptions = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            let error;\r\n            try {\r\n                await this._httpClient.delete(this._url, deleteOptions);\r\n            }\r\n            catch (err) {\r\n                error = err;\r\n            }\r\n            if (error) {\r\n                if (error instanceof _Errors__WEBPACK_IMPORTED_MODULE_4__.HttpError) {\r\n                    if (error.statusCode === 404) {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\r\n                    }\r\n                    else {\r\n                        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\r\n            }\r\n        }\r\n        finally {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n    _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n//# sourceMappingURL=LongPollingTransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageBuffer: () => (/* binding */ MessageBuffer)\n/* harmony export */ });\n/* harmony import */ var _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./IHubProtocol */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n/** @private */\r\nclass MessageBuffer {\r\n    constructor(protocol, connection, bufferSize) {\r\n        this._bufferSize = 100000;\r\n        this._messages = [];\r\n        this._totalMessageCount = 0;\r\n        this._waitForSequenceMessage = false;\r\n        // Message IDs start at 1 and always increment by 1\r\n        this._nextReceivingSequenceId = 1;\r\n        this._latestReceivedSequenceId = 0;\r\n        this._bufferedByteCount = 0;\r\n        this._reconnectInProgress = false;\r\n        this._protocol = protocol;\r\n        this._connection = connection;\r\n        this._bufferSize = bufferSize;\r\n    }\r\n    async _send(message) {\r\n        const serializedMessage = this._protocol.writeMessage(message);\r\n        let backpressurePromise = Promise.resolve();\r\n        // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\r\n        if (this._isInvocationMessage(message)) {\r\n            this._totalMessageCount++;\r\n            let backpressurePromiseResolver = () => { };\r\n            let backpressurePromiseRejector = () => { };\r\n            if ((0,_Utils__WEBPACK_IMPORTED_MODULE_0__.isArrayBuffer)(serializedMessage)) {\r\n                this._bufferedByteCount += serializedMessage.byteLength;\r\n            }\r\n            else {\r\n                this._bufferedByteCount += serializedMessage.length;\r\n            }\r\n            if (this._bufferedByteCount >= this._bufferSize) {\r\n                backpressurePromise = new Promise((resolve, reject) => {\r\n                    backpressurePromiseResolver = resolve;\r\n                    backpressurePromiseRejector = reject;\r\n                });\r\n            }\r\n            this._messages.push(new BufferedItem(serializedMessage, this._totalMessageCount, backpressurePromiseResolver, backpressurePromiseRejector));\r\n        }\r\n        try {\r\n            // If this is set it means we are reconnecting or resending\r\n            // We don't want to send on a disconnected connection\r\n            // And we don't want to send if resend is running since that would mean sending\r\n            // this message twice\r\n            if (!this._reconnectInProgress) {\r\n                await this._connection.send(serializedMessage);\r\n            }\r\n        }\r\n        catch {\r\n            this._disconnected();\r\n        }\r\n        await backpressurePromise;\r\n    }\r\n    _ack(ackMessage) {\r\n        let newestAckedMessage = -1;\r\n        // Find index of newest message being acked\r\n        for (let index = 0; index < this._messages.length; index++) {\r\n            const element = this._messages[index];\r\n            if (element._id <= ackMessage.sequenceId) {\r\n                newestAckedMessage = index;\r\n                if ((0,_Utils__WEBPACK_IMPORTED_MODULE_0__.isArrayBuffer)(element._message)) {\r\n                    this._bufferedByteCount -= element._message.byteLength;\r\n                }\r\n                else {\r\n                    this._bufferedByteCount -= element._message.length;\r\n                }\r\n                // resolve items that have already been sent and acked\r\n                element._resolver();\r\n            }\r\n            else if (this._bufferedByteCount < this._bufferSize) {\r\n                // resolve items that now fall under the buffer limit but haven't been acked\r\n                element._resolver();\r\n            }\r\n            else {\r\n                break;\r\n            }\r\n        }\r\n        if (newestAckedMessage !== -1) {\r\n            // We're removing everything including the message pointed to, so add 1\r\n            this._messages = this._messages.slice(newestAckedMessage + 1);\r\n        }\r\n    }\r\n    _shouldProcessMessage(message) {\r\n        if (this._waitForSequenceMessage) {\r\n            if (message.type !== _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Sequence) {\r\n                return false;\r\n            }\r\n            else {\r\n                this._waitForSequenceMessage = false;\r\n                return true;\r\n            }\r\n        }\r\n        // No special processing for acks, pings, etc.\r\n        if (!this._isInvocationMessage(message)) {\r\n            return true;\r\n        }\r\n        const currentId = this._nextReceivingSequenceId;\r\n        this._nextReceivingSequenceId++;\r\n        if (currentId <= this._latestReceivedSequenceId) {\r\n            if (currentId === this._latestReceivedSequenceId) {\r\n                // Should only hit this if we just reconnected and the server is sending\r\n                // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\r\n                this._ackTimer();\r\n            }\r\n            // Ignore, this is a duplicate message\r\n            return false;\r\n        }\r\n        this._latestReceivedSequenceId = currentId;\r\n        // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\r\n        // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\r\n        this._ackTimer();\r\n        return true;\r\n    }\r\n    _resetSequence(message) {\r\n        if (message.sequenceId > this._nextReceivingSequenceId) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\r\n            return;\r\n        }\r\n        this._nextReceivingSequenceId = message.sequenceId;\r\n    }\r\n    _disconnected() {\r\n        this._reconnectInProgress = true;\r\n        this._waitForSequenceMessage = true;\r\n    }\r\n    async _resend() {\r\n        const sequenceId = this._messages.length !== 0\r\n            ? this._messages[0]._id\r\n            : this._totalMessageCount + 1;\r\n        await this._connection.send(this._protocol.writeMessage({ type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Sequence, sequenceId }));\r\n        // Get a local variable to the _messages, just in case messages are acked while resending\r\n        // Which would slice the _messages array (which creates a new copy)\r\n        const messages = this._messages;\r\n        for (const element of messages) {\r\n            await this._connection.send(element._message);\r\n        }\r\n        this._reconnectInProgress = false;\r\n    }\r\n    _dispose(error) {\r\n        error !== null && error !== void 0 ? error : (error = new Error(\"Unable to reconnect to server.\"));\r\n        // Unblock backpressure if any\r\n        for (const element of this._messages) {\r\n            element._rejector(error);\r\n        }\r\n    }\r\n    _isInvocationMessage(message) {\r\n        // There is no way to check if something implements an interface.\r\n        // So we individually check the messages in a switch statement.\r\n        // To make sure we don't miss any message types we rely on the compiler\r\n        // seeing the function returns a value and it will do the\r\n        // exhaustive check for us on the switch statement, since we don't use 'case default'\r\n        switch (message.type) {\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Invocation:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.StreamItem:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Completion:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.StreamInvocation:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.CancelInvocation:\r\n                return true;\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Close:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Sequence:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Ping:\r\n            case _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Ack:\r\n                return false;\r\n        }\r\n    }\r\n    _ackTimer() {\r\n        if (this._ackTimerHandle === undefined) {\r\n            this._ackTimerHandle = setTimeout(async () => {\r\n                try {\r\n                    if (!this._reconnectInProgress) {\r\n                        await this._connection.send(this._protocol.writeMessage({ type: _IHubProtocol__WEBPACK_IMPORTED_MODULE_1__.MessageType.Ack, sequenceId: this._latestReceivedSequenceId }));\r\n                    }\r\n                    // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\r\n                }\r\n                catch { }\r\n                clearTimeout(this._ackTimerHandle);\r\n                this._ackTimerHandle = undefined;\r\n                // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\r\n            }, 1000);\r\n        }\r\n    }\r\n}\r\nclass BufferedItem {\r\n    constructor(message, id, resolver, rejector) {\r\n        this._message = message;\r\n        this._id = id;\r\n        this._resolver = resolver;\r\n        this._rejector = rejector;\r\n    }\r\n}\r\n//# sourceMappingURL=MessageBuffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServerSentEventsTransport: () => (/* binding */ ServerSentEventsTransport)\n/* harmony export */ });\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _ITransport__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ITransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n/** @private */\r\nclass ServerSentEventsTransport {\r\n    constructor(httpClient, accessToken, logger, options) {\r\n        this._httpClient = httpClient;\r\n        this._accessToken = accessToken;\r\n        this._logger = logger;\r\n        this._options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isRequired(url, \"url\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isRequired(transferFormat, \"transferFormat\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isIn(transferFormat, _ITransport__WEBPACK_IMPORTED_MODULE_1__.TransferFormat, \"transferFormat\");\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n        // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n        if (this._accessToken) {\r\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== _ITransport__WEBPACK_IMPORTED_MODULE_1__.TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n            let eventSource;\r\n            if (_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isBrowser || _Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource(url, { withCredentials: this._options.withCredentials });\r\n            }\r\n            else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = (0,_Utils__WEBPACK_IMPORTED_MODULE_0__.getUserAgentHeader)();\r\n                headers[name] = value;\r\n                eventSource = new this._options.EventSource(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers } });\r\n            }\r\n            try {\r\n                eventSource.onmessage = (e) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Trace, `(SSE transport) data received. ${(0,_Utils__WEBPACK_IMPORTED_MODULE_0__.getDataDetail)(e.data, this._options.logMessageContent)}.`);\r\n                            this.onreceive(e.data);\r\n                        }\r\n                        catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    }\r\n                    else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                            + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                            + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n    async send(data) {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return (0,_Utils__WEBPACK_IMPORTED_MODULE_0__.sendMessage)(this._logger, \"SSE\", this._httpClient, this._url, data, this._options);\r\n    }\r\n    stop() {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n    _close(e) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n//# sourceMappingURL=ServerSentEventsTransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/Subject.js":
/*!*************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/Subject.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subject: () => (/* binding */ Subject)\n/* harmony export */ });\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n/** Stream implementation to stream items to the server. */\r\nclass Subject {\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n    next(item) {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n    error(err) {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n    complete() {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n    subscribe(observer) {\r\n        this.observers.push(observer);\r\n        return new _Utils__WEBPACK_IMPORTED_MODULE_0__.SubjectSubscription(this, observer);\r\n    }\r\n}\r\n//# sourceMappingURL=Subject.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/Subject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextMessageFormat: () => (/* binding */ TextMessageFormat)\n/* harmony export */ });\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// Not exported from index\r\n/** @private */\r\nclass TextMessageFormat {\r\n    static write(output) {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n    static parse(input) {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\nTextMessageFormat.RecordSeparatorCode = 0x1e;\r\nTextMessageFormat.RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n//# sourceMappingURL=TextMessageFormat.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG1pY3Jvc29mdC9zaWduYWxyL2Rpc3QvZXNtL1RleHRNZXNzYWdlRm9ybWF0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxrQkFBa0IsT0FBTyxFQUFFLGtDQUFrQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXENhcHN0b25lUHJvamVjdFxcT3BlbkF1dG9tYXRlLkZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBtaWNyb3NvZnRcXHNpZ25hbHJcXGRpc3RcXGVzbVxcVGV4dE1lc3NhZ2VGb3JtYXQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gTGljZW5zZWQgdG8gdGhlIC5ORVQgRm91bmRhdGlvbiB1bmRlciBvbmUgb3IgbW9yZSBhZ3JlZW1lbnRzLlxyXG4vLyBUaGUgLk5FVCBGb3VuZGF0aW9uIGxpY2Vuc2VzIHRoaXMgZmlsZSB0byB5b3UgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxyXG4vLyBOb3QgZXhwb3J0ZWQgZnJvbSBpbmRleFxyXG4vKiogQHByaXZhdGUgKi9cclxuZXhwb3J0IGNsYXNzIFRleHRNZXNzYWdlRm9ybWF0IHtcclxuICAgIHN0YXRpYyB3cml0ZShvdXRwdXQpIHtcclxuICAgICAgICByZXR1cm4gYCR7b3V0cHV0fSR7VGV4dE1lc3NhZ2VGb3JtYXQuUmVjb3JkU2VwYXJhdG9yfWA7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgcGFyc2UoaW5wdXQpIHtcclxuICAgICAgICBpZiAoaW5wdXRbaW5wdXQubGVuZ3RoIC0gMV0gIT09IFRleHRNZXNzYWdlRm9ybWF0LlJlY29yZFNlcGFyYXRvcikge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJNZXNzYWdlIGlzIGluY29tcGxldGUuXCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBtZXNzYWdlcyA9IGlucHV0LnNwbGl0KFRleHRNZXNzYWdlRm9ybWF0LlJlY29yZFNlcGFyYXRvcik7XHJcbiAgICAgICAgbWVzc2FnZXMucG9wKCk7XHJcbiAgICAgICAgcmV0dXJuIG1lc3NhZ2VzO1xyXG4gICAgfVxyXG59XHJcblRleHRNZXNzYWdlRm9ybWF0LlJlY29yZFNlcGFyYXRvckNvZGUgPSAweDFlO1xyXG5UZXh0TWVzc2FnZUZvcm1hdC5SZWNvcmRTZXBhcmF0b3IgPSBTdHJpbmcuZnJvbUNoYXJDb2RlKFRleHRNZXNzYWdlRm9ybWF0LlJlY29yZFNlcGFyYXRvckNvZGUpO1xyXG4vLyMgc291cmNlTWFwcGluZ1VSTD1UZXh0TWVzc2FnZUZvcm1hdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js":
/*!***********************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/Utils.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arg: () => (/* binding */ Arg),\n/* harmony export */   ConsoleLogger: () => (/* binding */ ConsoleLogger),\n/* harmony export */   Platform: () => (/* binding */ Platform),\n/* harmony export */   SubjectSubscription: () => (/* binding */ SubjectSubscription),\n/* harmony export */   VERSION: () => (/* binding */ VERSION),\n/* harmony export */   constructUserAgent: () => (/* binding */ constructUserAgent),\n/* harmony export */   createLogger: () => (/* binding */ createLogger),\n/* harmony export */   formatArrayBuffer: () => (/* binding */ formatArrayBuffer),\n/* harmony export */   getDataDetail: () => (/* binding */ getDataDetail),\n/* harmony export */   getErrorString: () => (/* binding */ getErrorString),\n/* harmony export */   getGlobalThis: () => (/* binding */ getGlobalThis),\n/* harmony export */   getUserAgentHeader: () => (/* binding */ getUserAgentHeader),\n/* harmony export */   isArrayBuffer: () => (/* binding */ isArrayBuffer),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _Loggers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Loggers */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Loggers.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\nconst VERSION = \"8.0.7\";\r\n/** @private */\r\nclass Arg {\r\n    static isRequired(val, name) {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    static isNotEmpty(val, name) {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n    static isIn(val, values, name) {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n/** @private */\r\nclass Platform {\r\n    // react-native has a window but no document so we should check both\r\n    static get isBrowser() {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    static get isWebWorker() {\r\n        return !Platform.isNode && typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n    // react-native has a window but no document\r\n    static get isReactNative() {\r\n        return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    static get isNode() {\r\n        return typeof process !== \"undefined\" && process.release && process.release.name === \"node\";\r\n    }\r\n}\r\n/** @private */\r\nfunction getDataDetail(data, includeContent) {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    }\r\n    else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n/** @private */\r\nfunction formatArrayBuffer(data) {\r\n    const view = new Uint8Array(data);\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nfunction isArrayBuffer(val) {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n/** @private */\r\nasync function sendMessage(logger, transportName, httpClient, url, content, options) {\r\n    const headers = {};\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n    logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent)}.`);\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers },\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n    logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n/** @private */\r\nfunction createLogger(logger) {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information);\r\n    }\r\n    if (logger === null) {\r\n        return _Loggers__WEBPACK_IMPORTED_MODULE_1__.NullLogger.instance;\r\n    }\r\n    if (logger.log !== undefined) {\r\n        return logger;\r\n    }\r\n    return new ConsoleLogger(logger);\r\n}\r\n/** @private */\r\nclass SubjectSubscription {\r\n    constructor(subject, observer) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n    dispose() {\r\n        const index = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n/** @private */\r\nclass ConsoleLogger {\r\n    constructor(minimumLogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n    log(logLevel, message) {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${_ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Critical:\r\n                case _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case _ILogger__WEBPACK_IMPORTED_MODULE_0__.LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n/** @private */\r\nfunction getUserAgentHeader() {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion())];\r\n}\r\n/** @private */\r\nfunction constructUserAgent(version, os, runtime, runtimeVersion) {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent = \"Microsoft SignalR/\";\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    }\r\n    else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n    userAgent += `${runtime}`;\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    }\r\n    else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName() {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    }\r\n    else {\r\n        return \"\";\r\n    }\r\n}\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion() {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\nfunction getRuntime() {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    }\r\n    else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n/** @private */\r\nfunction getErrorString(e) {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    }\r\n    else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n/** @private */\r\nfunction getGlobalThis() {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}\r\n//# sourceMappingURL=Utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js":
/*!************************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketTransport: () => (/* binding */ WebSocketTransport)\n/* harmony export */ });\n/* harmony import */ var _HeaderNames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./HeaderNames */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HeaderNames.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _ITransport__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ITransport */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ITransport.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\n/** @private */\r\nclass WebSocketTransport {\r\n    constructor(httpClient, accessTokenFactory, logger, logMessageContent, webSocketConstructor, headers) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isRequired(url, \"url\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isRequired(transferFormat, \"transferFormat\");\r\n        _Utils__WEBPACK_IMPORTED_MODULE_0__.Arg.isIn(transferFormat, _ITransport__WEBPACK_IMPORTED_MODULE_1__.TransferFormat, \"transferFormat\");\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n        let token;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n            if (_Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isNode || _Utils__WEBPACK_IMPORTED_MODULE_0__.Platform.isReactNative) {\r\n                const headers = {};\r\n                const [name, value] = (0,_Utils__WEBPACK_IMPORTED_MODULE_0__.getUserAgentHeader)();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[_HeaderNames__WEBPACK_IMPORTED_MODULE_3__.HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n                if (cookies) {\r\n                    headers[_HeaderNames__WEBPACK_IMPORTED_MODULE_3__.HeaderNames.Cookie] = cookies;\r\n                }\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n            if (transferFormat === _ITransport__WEBPACK_IMPORTED_MODULE_1__.TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n            webSocket.onopen = (_event) => {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n            webSocket.onerror = (event) => {\r\n                let error = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                }\r\n                else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n            webSocket.onmessage = (message) => {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Trace, `(WebSockets transport) data received. ${(0,_Utils__WEBPACK_IMPORTED_MODULE_0__.getDataDetail)(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    }\r\n                    catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n            webSocket.onclose = (event) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                }\r\n                else {\r\n                    let error = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    }\r\n                    else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                            + \" either the endpoint may not be a SignalR endpoint,\"\r\n                            + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                            + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n    send(data) {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Trace, `(WebSockets transport) sending data. ${(0,_Utils__WEBPACK_IMPORTED_MODULE_0__.getDataDetail)(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n    stop() {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n    _close(event) {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => { };\r\n            this._webSocket.onmessage = () => { };\r\n            this._webSocket.onerror = () => { };\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n        this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_2__.LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            }\r\n            else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            }\r\n            else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n    _isCloseEvent(event) {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n//# sourceMappingURL=WebSocketTransport.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   XhrHttpClient: () => (/* binding */ XhrHttpClient)\n/* harmony export */ });\n/* harmony import */ var _Errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Errors */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Errors.js\");\n/* harmony import */ var _HttpClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./HttpClient */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/HttpClient.js\");\n/* harmony import */ var _ILogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ILogger */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/ILogger.js\");\n/* harmony import */ var _Utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils */ \"(ssr)/./node_modules/@microsoft/signalr/dist/esm/Utils.js\");\n// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n\r\n\r\n\r\nclass XhrHttpClient extends _HttpClient__WEBPACK_IMPORTED_MODULE_0__.HttpClient {\r\n    constructor(logger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n    /** @inheritDoc */\r\n    send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new _Errors__WEBPACK_IMPORTED_MODULE_1__.AbortError());\r\n        }\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n            xhr.open(request.method, request.url, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if ((0,_Utils__WEBPACK_IMPORTED_MODULE_2__.isArrayBuffer)(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                }\r\n                else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                    xhr.setRequestHeader(header, headers[header]);\r\n                });\r\n            }\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new _Errors__WEBPACK_IMPORTED_MODULE_1__.AbortError());\r\n                };\r\n            }\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new _HttpClient__WEBPACK_IMPORTED_MODULE_0__.HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                }\r\n                else {\r\n                    reject(new _Errors__WEBPACK_IMPORTED_MODULE_1__.HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n            xhr.onerror = () => {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new _Errors__WEBPACK_IMPORTED_MODULE_1__.HttpError(xhr.statusText, xhr.status));\r\n            };\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(_ILogger__WEBPACK_IMPORTED_MODULE_3__.LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new _Errors__WEBPACK_IMPORTED_MODULE_1__.TimeoutError());\r\n            };\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n//# sourceMappingURL=XhrHttpClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js\n");

/***/ })

};
;