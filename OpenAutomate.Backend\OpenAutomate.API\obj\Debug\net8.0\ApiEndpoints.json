[{"ContainingType": "OpenAutomate.API.Controllers.BotAgentConnectionController", "Method": "SendCommandToBotAgent", "RelativePath": "{tenant}/api/agent-connection/{id}/command", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "OpenAutomate.Core.Dto.BotAgent.BotAgentCommandDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentConnectionController", "Method": "GetBotAgentStatus", "RelativePath": "{tenant}/api/agent-connection/{id}/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentConnectionController", "Method": "BroadcastNotification", "RelativePath": "{tenant}/api/agent-connection/broadcast", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "notification", "Type": "OpenAutomate.Core.Dto.BotAgent.BroadcastNotificationDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentConnectionController", "Method": "ConnectBotAgent", "RelativePath": "{tenant}/api/agent-connection/connect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "connectionRequest", "Type": "OpenAutomate.Core.Dto.BotAgent.BotAgentConnectionRequest", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "GetAllBotAgents", "RelativePath": "{tenant}/api/agents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.BotAgent.BotAgentResponseDto, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "GetBotAgentById", "RelativePath": "{tenant}/api/agents/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.BotAgent.BotAgentResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "DeleteBotAgent", "RelativePath": "{tenant}/api/agents/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "UpdateBotAgent", "RelativePath": "{tenant}/api/agents/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "OpenAutomate.Core.Dto.BotAgent.UpdateBotAgentDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.BotAgent.BotAgentResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "DeactivateBotAgent", "RelativePath": "{tenant}/api/agents/{id}/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "RegenerateMachineKey", "RelativePath": "{tenant}/api/agents/{id}/regenerateKey", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.BotAgent.BotAgentResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentController", "Method": "CreateBotAgent", "RelativePath": "{tenant}/api/agents/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "OpenAutomate.Core.Dto.BotAgent.CreateBotAgentDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.BotAgent.BotAgentResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "CreateAsset", "RelativePath": "{tenant}/api/assets", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "OpenAutomate.Core.Dto.Asset.CreateAssetDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Asset.AssetResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "GetAllAssets", "RelativePath": "{tenant}/api/assets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.Asset.AssetListResponseDto, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "GetAssetById", "RelativePath": "{tenant}/api/assets/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Asset.AssetResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "UpdateAsset", "RelativePath": "{tenant}/api/assets/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "OpenAutomate.Core.Dto.Asset.UpdateAssetDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Asset.AssetResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "DeleteAsset", "RelativePath": "{tenant}/api/assets/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "GetAuthorizedBotAgents", "RelativePath": "{tenant}/api/assets/{id}/bot-agents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.BotAgent.BotAgentSummaryDto, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "UpdateAuthorizedBotAgents", "RelativePath": "{tenant}/api/assets/{id}/bot-agents", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "OpenAutomate.Core.Dto.Asset.AssetBotAgentDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "AuthorizeBotAgent", "RelativePath": "{tenant}/api/assets/{id}/bot-agents/{botAgentId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "botAgentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "RevokeBotAgent", "RelativePath": "{tenant}/api/assets/{id}/bot-agents/{botAgentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "botAgentId", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AssetController", "Method": "GetAssetByKey", "RelativePath": "{tenant}/api/assets/key/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Asset.AssetResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthorController", "Method": "AddResourcePermission", "RelativePath": "{tenant}/api/author/permission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "OpenAutomate.Core.Dto.Authority.ResourcePermissionDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthorController", "Method": "RemoveResourcePermission", "RelativePath": "{tenant}/api/author/permission/{authorityName}/{resourceName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "authorityName", "Type": "System.String", "IsRequired": true}, {"Name": "resourceName", "Type": "System.String", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthorController", "Method": "GetUserAuthorities", "RelativePath": "{tenant}/api/author/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthorController", "Method": "AssignAuthorityToUser", "RelativePath": "{tenant}/api/author/user/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "OpenAutomate.Core.Dto.Authority.AssignAuthorityDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthorController", "Method": "RemoveAuthorityFromUser", "RelativePath": "{tenant}/api/author/user/{userId}/{authorityName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "authorityName", "Type": "System.String", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentAssetController", "Method": "GetAccessibleAssets", "RelativePath": "{tenant}/api/bot-agent/assets/accessible", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.Asset.BotAgentKeyDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.BotAgentAssetController", "Method": "GetAssetValueByKey", "RelativePath": "{tenant}/api/bot-agent/assets/key/{key}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "OpenAutomate.Core.Dto.Asset.BotAgentAssetDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.ExecutionController", "Method": "GetAllExecutions", "RelativePath": "{tenant}/api/executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.Execution.ExecutionResponseDto, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.ExecutionController", "Method": "GetExecutionById", "RelativePath": "{tenant}/api/executions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Execution.ExecutionResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.ExecutionController", "Method": "CancelExecution", "RelativePath": "{tenant}/api/executions/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Execution.ExecutionResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.ExecutionController", "Method": "UpdateExecutionStatus", "RelativePath": "{tenant}/api/executions/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateDto", "Type": "OpenAutomate.Core.Dto.Execution.UpdateExecutionStatusDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Execution.ExecutionResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.ExecutionController", "Method": "TriggerExecution", "RelativePath": "{tenant}/api/executions/trigger", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "OpenAutomate.Core.Dto.Execution.TriggerExecutionDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Execution.ExecutionResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitInvitationController", "Method": "InviteUser", "RelativePath": "{tenant}/api/organization-unit-invitation", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "OpenAutomate.Core.Dto.OrganizationUnitInvitation.InviteUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitInvitationController", "Method": "AcceptInvitation", "RelativePath": "{tenant}/api/organization-unit-invitation/accept", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.OrganizationUnitInvitation.AcceptInvitationRequest", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitInvitationController", "Method": "CheckInvitation", "RelativePath": "{tenant}/api/organization-unit-invitation/check", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "System.String", "IsRequired": true}, {"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitInvitationController", "Method": "CheckInvitationToken", "RelativePath": "{tenant}/api/organization-unit-invitation/check-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": false}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "CreatePackage", "RelativePath": "{tenant}/api/packages", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "OpenAutomate.Core.Dto.Package.CreateAutomationPackageDto", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Package.AutomationPackageResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "GetAllPackages", "RelativePath": "{tenant}/api/packages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.Package.AutomationPackageResponseDto, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "GetPackageById", "RelativePath": "{tenant}/api/packages/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Package.AutomationPackageResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "DeletePackage", "RelativePath": "{tenant}/api/packages/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "UploadPackageVersion", "RelativePath": "{tenant}/api/packages/{id}/versions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Version", "Type": "System.String", "IsRequired": false}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Package.PackageVersionResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "DeletePackageVersion", "RelativePath": "{tenant}/api/packages/{id}/versions/{version}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "GetAgentDownloadUrl", "RelativePath": "{tenant}/api/packages/{id}/versions/{version}/agent-download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "machineKey", "Type": "System.String", "IsRequired": false}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "GetPackageDownloadUrl", "RelativePath": "{tenant}/api/packages/{id}/versions/{version}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.AutomationPackageController", "Method": "UploadPackageWithAutoCreation", "RelativePath": "{tenant}/api/packages/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "Version", "Type": "System.String", "IsRequired": false}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.Package.AutomationPackageResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.OData.AssetsController", "Method": "Get", "RelativePath": "{tenant}/odata/Assets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.AssetsController", "Method": "Get", "RelativePath": "{tenant}/odata/Assets/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.AutomationPackagesController", "Method": "Get", "RelativePath": "{tenant}/odata/AutomationPackages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.AutomationPackagesController", "Method": "Get", "RelativePath": "{tenant}/odata/AutomationPackages/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.BotAgentsController", "Method": "Get", "RelativePath": "{tenant}/odata/BotAgents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.BotAgentsController", "Method": "Get", "RelativePath": "{tenant}/odata/BotAgents/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.ExecutionsController", "Method": "Get", "RelativePath": "{tenant}/odata/Executions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.ExecutionsController", "Method": "Get", "RelativePath": "{tenant}/odata/Executions/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.PackageVersionsController", "Method": "Get", "RelativePath": "{tenant}/odata/PackageVersions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.PackageVersionsController", "Method": "Get", "RelativePath": "{tenant}/odata/PackageVersions/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.UsersController", "Method": "Get", "RelativePath": "{tenant}/odata/Users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OData.UsersController", "Method": "Get", "RelativePath": "{tenant}/odata/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.Guid", "IsRequired": true}, {"Name": "tenant", "Type": "", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.AdminController", "Method": "GetAllOrganizationUnit", "RelativePath": "api/admin/organization-unit/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.UserDto.UserResponse, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AdminController", "Method": "ChangePassword", "RelativePath": "api/admin/user/change-password/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "OpenAutomate.Core.Dto.AdminDto.AdminChangePasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AdminController", "Method": "GetUserById", "RelativePath": "api/admin/user/detail/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.UserDto.UserResponse", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.AdminController", "Method": "GetAllUsers", "RelativePath": "api/admin/user/get-all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.UserDto.UserResponse, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "OpenAutomate.API.Controllers.AdminController", "Method": "UpdateUserInfo", "RelativePath": "api/admin/user/update-detail/{userId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.UpdateUserInfoRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.UserDto.UserResponse", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "ForgotPassword", "RelativePath": "api/authen/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "<PERSON><PERSON>", "RelativePath": "api/authen/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.AuthenticationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "RefreshToken", "RelativePath": "api/authen/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "Register", "RelativePath": "api/authen/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.RegistrationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "ResetPassword", "RelativePath": "api/authen/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "RevokeToken", "RelativePath": "api/authen/revoke-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.RevokeTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.AuthenController", "Method": "GetCurrentUser", "RelativePath": "api/authen/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.UserDto.UserResponse", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.EmailVerificationController", "Method": "ResendVerification", "RelativePath": "api/email/resend", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.EmailVerificationController", "Method": "VerifyEmail", "RelativePath": "api/email/verify", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "token", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 302}]}, {"ContainingType": "OpenAutomate.API.Controllers.EmailTestController", "Method": "SendWelcomeEmailGet", "RelativePath": "api/EmailTest/send-welcome", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.EmailTestController", "Method": "SendWelcomeEmailPost", "RelativePath": "api/EmailTest/send-welcome", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.EmailTestController", "Method": "GetEmailStatus", "RelativePath": "api/EmailTest/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "GetAll", "RelativePath": "api/ou", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[OpenAutomate.Core.Dto.OrganizationUnit.OrganizationUnitResponseDto, OpenAutomate.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "GetById", "RelativePath": "api/ou/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.OrganizationUnit.OrganizationUnitResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "Update", "RelativePath": "api/ou/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "dto", "Type": "OpenAutomate.Core.Dto.OrganizationUnit.CreateOrganizationUnitDto", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.OrganizationUnit.OrganizationUnitResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "CheckNameChange", "RelativePath": "api/ou/{id}/check-name-change", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "newName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.OrganizationUnit.SlugChangeWarningDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "Create", "RelativePath": "api/ou/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "OpenAutomate.Core.Dto.OrganizationUnit.CreateOrganizationUnitDto", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.OrganizationUnit.OrganizationUnitResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "GetMyOrganizationUnits", "RelativePath": "api/ou/my-ous", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.OrganizationUnit.UserOrganizationUnitsResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.OrganizationUnitController", "Method": "GetBySlug", "RelativePath": "api/ou/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.OrganizationUnit.OrganizationUnitResponseDto", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "OpenAutomate.API.Controllers.UserController", "Method": "ChangePassword", "RelativePath": "api/user/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "OpenAutomate.API.Controllers.UserController", "Method": "UpdateUserInfo", "RelativePath": "api/user/user", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "OpenAutomate.Core.Dto.UserDto.UpdateUserInfoRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "OpenAutomate.Core.Dto.UserDto.UserResponse", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json;odata.metadata=minimal;odata.streaming=true", "application/json;odata.metadata=minimal;odata.streaming=false", "application/json;odata.metadata=minimal", "application/json;odata.metadata=full;odata.streaming=true", "application/json;odata.metadata=full;odata.streaming=false", "application/json;odata.metadata=full", "application/json;odata.metadata=none;odata.streaming=true", "application/json;odata.metadata=none;odata.streaming=false", "application/json;odata.metadata=none", "application/json;odata.streaming=true", "application/json;odata.streaming=false", "application/json", "application/xml", "application/json", "text/plain", "application/octet-stream", "text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}]