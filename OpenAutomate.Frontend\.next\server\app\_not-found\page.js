/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast-provider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Clocale-provider.tsx%22%2C%22ids%22%3A%5B%22LocaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast-provider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Clocale-provider.tsx%22%2C%22ids%22%3A%5B%22LocaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast-provider.tsx */ \"(rsc)/./src/components/ui/toast-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(rsc)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider.tsx */ \"(rsc)/./src/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/locale-provider.tsx */ \"(rsc)/./src/providers/locale-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/theme-provider.tsx */ \"(rsc)/./src/providers/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNDYXBzdG9uZVByb2plY3QlNUMlNUNPcGVuQXV0b21hdGUuRnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0NhcHN0b25lUHJvamVjdCU1QyU1Q09wZW5BdXRvbWF0ZS5Gcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNDYXBzdG9uZVByb2plY3QlNUMlNUNPcGVuQXV0b21hdGUuRnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN0b2FzdC1wcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNDYXBzdG9uZVByb2plY3QlNUMlNUNPcGVuQXV0b21hdGUuRnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDdWklNUMlNUN0b2FzdGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0NhcHN0b25lUHJvamVjdCU1QyU1Q09wZW5BdXRvbWF0ZS5Gcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q2F1dGgtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQXV0aFByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNDYXBzdG9uZVByb2plY3QlNUMlNUNPcGVuQXV0b21hdGUuRnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNwcm92aWRlcnMlNUMlNUNsb2NhbGUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTG9jYWxlUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0NhcHN0b25lUHJvamVjdCU1QyU1Q09wZW5BdXRvbWF0ZS5Gcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q3RoZW1lLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdMQUEySjtBQUMzSjtBQUNBLDBLQUE4STtBQUM5STtBQUNBLDhLQUFvSjtBQUNwSjtBQUNBLGtMQUF3SjtBQUN4SjtBQUNBLGdMQUFzSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RQcm92aWRlclwiXSAqLyBcIkQ6XFxcXENhcHN0b25lUHJvamVjdFxcXFxPcGVuQXV0b21hdGUuRnJvbnRlbmRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcdG9hc3QtcHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUb2FzdGVyXCJdICovIFwiRDpcXFxcQ2Fwc3RvbmVQcm9qZWN0XFxcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFx1aVxcXFx0b2FzdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQXV0aFByb3ZpZGVyXCJdICovIFwiRDpcXFxcQ2Fwc3RvbmVQcm9qZWN0XFxcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcXFxzcmNcXFxccHJvdmlkZXJzXFxcXGF1dGgtcHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJMb2NhbGVQcm92aWRlclwiXSAqLyBcIkQ6XFxcXENhcHN0b25lUHJvamVjdFxcXFxPcGVuQXV0b21hdGUuRnJvbnRlbmRcXFxcc3JjXFxcXHByb3ZpZGVyc1xcXFxsb2NhbGUtcHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxcQ2Fwc3RvbmVQcm9qZWN0XFxcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcXFxzcmNcXFxccHJvdmlkZXJzXFxcXHRoZW1lLXByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast-provider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Clocale-provider.tsx%22%2C%22ids%22%3A%5B%22LocaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f12820de0a4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQ2Fwc3RvbmVQcm9qZWN0XFxPcGVuQXV0b21hdGUuRnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVmMTI4MjBkZTBhNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/theme-provider */ \"(rsc)/./src/providers/theme-provider.tsx\");\n/* harmony import */ var _providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/auth-provider */ \"(rsc)/./src/providers/auth-provider.tsx\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./src/lib/config.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var _components_ui_toast_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toast-provider */ \"(rsc)/./src/components/ui/toast-provider.tsx\");\n/* harmony import */ var _providers_locale_provider__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/locale-provider */ \"(rsc)/./src/providers/locale-provider.tsx\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: _lib_config__WEBPACK_IMPORTED_MODULE_4__.config.app.name,\n    description: 'Automate your business processes with OpenAutomate',\n    authors: [\n        {\n            name: 'OpenAutomate Team'\n        }\n    ],\n    metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000')\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_8___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_locale_provider__WEBPACK_IMPORTED_MODULE_7__.LocaleProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"dark\",\n                    enableSystem: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast_provider__WEBPACK_IMPORTED_MODULE_6__.ToastProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"min-h-screen flex flex-col antialiased bg-background\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_5__.Toaster, {}, void 0, false, {\n                                    fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/toast-provider.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/toast-provider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ToastProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\toast-provider.tsx",
"ToastProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\components\\ui\\toaster.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/**\r\n * Central configuration settings for the application\r\n * Makes configuration values accessible throughout the app\r\n */ const config = {\n    /**\r\n   * Application information\r\n   */ app: {\n        /**\r\n     * Application name\r\n     */ name: 'OpenAutomate Orchestrator',\n        /**\r\n     * Application URL - different for dev and production\r\n     */ url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n        /**\r\n     * Production domain - used for cookies in production\r\n     */ domain:  false ? 0 : 'localhost'\n    },\n    /**\r\n   * API configuration\r\n   */ api: {\n        /**\r\n     * Base URL for API requests\r\n     */ baseUrl:  false ? 0 : \"http://localhost:5252\" || 0,\n        /**\r\n     * Default headers for API requests\r\n     */ defaultHeaders: {\n            'Content-Type': 'application/json',\n            Accept: 'application/json'\n        }\n    },\n    /**\r\n   * Authentication configuration\r\n   */ auth: {\n        /**\r\n     * Token refresh interval in milliseconds (14 minutes)\r\n     */ tokenRefreshInterval: 14 * 60 * 1000,\n        /**\r\n     * Token storage key in localStorage\r\n     */ tokenStorageKey: 'auth_token',\n        /**\r\n     * User data storage key in localStorage\r\n     */ userStorageKey: 'user_data'\n    },\n    /**\r\n   * URL paths\r\n   */ paths: {\n        /**\r\n     * Authentication-related paths\r\n     */ auth: {\n            login: '/login',\n            register: '/register',\n            forgotPassword: '/forgot-password',\n            resetPassword: '/reset-password',\n            verificationPending: '/verification-pending',\n            emailVerified: '/email-verified',\n            verifyEmail: '/verify-email',\n            organizationSelector: '/tenant-selector'\n        },\n        /**\r\n     * Default redirect after login\r\n     */ defaultRedirect: '/tenant-selector'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/providers/auth-provider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/auth-provider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthContext: () => (/* binding */ AuthContext),
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthContext() from the server but AuthContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auth-provider.tsx",
"AuthContext",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auth-provider.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\auth-provider.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./src/providers/locale-provider.tsx":
/*!*******************************************!*\
  !*** ./src/providers/locale-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LocaleProvider: () => (/* binding */ LocaleProvider),
/* harmony export */   useLocale: () => (/* binding */ useLocale)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LocaleProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LocaleProvider() from the server but LocaleProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\locale-provider.tsx",
"LocaleProvider",
);const useLocale = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLocale() from the server but useLocale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\locale-provider.tsx",
"useLocale",
);

/***/ }),

/***/ "(rsc)/./src/providers/theme-provider.tsx":
/*!******************************************!*\
  !*** ./src/providers/theme-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\CapstoneProject\\OpenAutomate.Frontend\\src\\providers\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast-provider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Clocale-provider.tsx%22%2C%22ids%22%3A%5B%22LocaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast-provider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Clocale-provider.tsx%22%2C%22ids%22%3A%5B%22LocaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast-provider.tsx */ \"(ssr)/./src/components/ui/toast-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toaster.tsx */ \"(ssr)/./src/components/ui/toaster.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/auth-provider.tsx */ \"(ssr)/./src/providers/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/locale-provider.tsx */ \"(ssr)/./src/providers/locale-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/theme-provider.tsx */ \"(ssr)/./src/providers/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast-provider.tsx%22%2C%22ids%22%3A%5B%22ToastProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Clocale-provider.tsx%22%2C%22ids%22%3A%5B%22LocaleProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CCapstoneProject%5C%5COpenAutomate.Frontend%5C%5Csrc%5C%5Cproviders%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast-provider.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/toast-provider.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-toast */ \"(ssr)/./src/components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider auto */ \n\n\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (toast)=>{\n        const id = toast.id || crypto.randomUUID();\n        setToasts((prev)=>{\n            // Check if we need to limit the number of toasts\n            const newToasts = [\n                {\n                    ...toast,\n                    id\n                },\n                ...prev\n            ];\n            return newToasts.slice(0, 5) // Limit to 5 toasts\n            ;\n        });\n        return id;\n    };\n    const updateToast = (toast)=>{\n        if (!toast.id) return;\n        setToasts((prev)=>prev.map((t)=>t.id === toast.id ? {\n                    ...t,\n                    ...toast\n                } : t));\n    };\n    const dismissToast = (toastId)=>{\n        setToasts((prev)=>prev.map((t)=>t.id === toastId ? {\n                    ...t,\n                    open: false\n                } : t));\n    };\n    const removeToast = (toastId)=>{\n        setToasts((prev)=>prev.filter((t)=>t.id !== toastId));\n    };\n    // Memoize the context value to prevent unnecessary re-renders\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ToastProvider.useMemo[contextValue]\": ()=>({\n                toasts,\n                addToast,\n                updateToast,\n                dismissToast,\n                removeToast\n            })\n    }[\"ToastProvider.useMemo[contextValue]\"], [\n        toasts\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_use_toast__WEBPACK_IMPORTED_MODULE_2__.ToastContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast-provider.tsx\",\n        lineNumber: 47,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS90b2FzdC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVvRDtBQUNNO0FBRW5ELFNBQVNHLGNBQWMsRUFBRUMsUUFBUSxFQUEyQjtJQUNqRSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR04sK0NBQVFBLENBQWMsRUFBRTtJQUVwRCxNQUFNTyxXQUFXLENBQUNDO1FBQ2hCLE1BQU1DLEtBQUtELE1BQU1DLEVBQUUsSUFBSUMsT0FBT0MsVUFBVTtRQUV4Q0wsVUFBVSxDQUFDTTtZQUNULGlEQUFpRDtZQUNqRCxNQUFNQyxZQUFZO2dCQUFDO29CQUFFLEdBQUdMLEtBQUs7b0JBQUVDO2dCQUFHO21CQUFNRzthQUFLO1lBQzdDLE9BQU9DLFVBQVVDLEtBQUssQ0FBQyxHQUFHLEdBQUcsb0JBQW9COztRQUNuRDtRQUVBLE9BQU9MO0lBQ1Q7SUFFQSxNQUFNTSxjQUFjLENBQUNQO1FBQ25CLElBQUksQ0FBQ0EsTUFBTUMsRUFBRSxFQUFFO1FBRWZILFVBQVUsQ0FBQ00sT0FBU0EsS0FBS0ksR0FBRyxDQUFDLENBQUNDLElBQU9BLEVBQUVSLEVBQUUsS0FBS0QsTUFBTUMsRUFBRSxHQUFHO29CQUFFLEdBQUdRLENBQUM7b0JBQUUsR0FBR1QsS0FBSztnQkFBQyxJQUFJUztJQUNoRjtJQUVBLE1BQU1DLGVBQWUsQ0FBQ0M7UUFDcEJiLFVBQVUsQ0FBQ00sT0FBU0EsS0FBS0ksR0FBRyxDQUFDLENBQUNDLElBQU9BLEVBQUVSLEVBQUUsS0FBS1UsVUFBVTtvQkFBRSxHQUFHRixDQUFDO29CQUFFRyxNQUFNO2dCQUFNLElBQUlIO0lBQ2xGO0lBRUEsTUFBTUksY0FBYyxDQUFDRjtRQUNuQmIsVUFBVSxDQUFDTSxPQUFTQSxLQUFLVSxNQUFNLENBQUMsQ0FBQ0wsSUFBTUEsRUFBRVIsRUFBRSxLQUFLVTtJQUNsRDtJQUVBLDhEQUE4RDtJQUM5RCxNQUFNSSxlQUFldEIsOENBQU9BOytDQUMxQixJQUFPO2dCQUNMSTtnQkFDQUU7Z0JBQ0FRO2dCQUNBRztnQkFDQUc7WUFDRjs4Q0FDQTtRQUFDaEI7S0FBTztJQUdWLHFCQUFPLDhEQUFDSCxvREFBWUEsQ0FBQ3NCLFFBQVE7UUFBQ0MsT0FBT0Y7a0JBQWVuQjs7Ozs7O0FBQ3REIiwic291cmNlcyI6WyJEOlxcQ2Fwc3RvbmVQcm9qZWN0XFxPcGVuQXV0b21hdGUuRnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcdWlcXHRvYXN0LXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7IFJlYWN0Tm9kZSwgdXNlU3RhdGUsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHsgVG9hc3RDb250ZXh0LCB0eXBlIFRvYXN0VHlwZSB9IGZyb20gJy4vdXNlLXRvYXN0J1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFRvYXN0UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdE5vZGUgfSkge1xyXG4gIGNvbnN0IFt0b2FzdHMsIHNldFRvYXN0c10gPSB1c2VTdGF0ZTxUb2FzdFR5cGVbXT4oW10pXHJcblxyXG4gIGNvbnN0IGFkZFRvYXN0ID0gKHRvYXN0OiBUb2FzdFR5cGUpID0+IHtcclxuICAgIGNvbnN0IGlkID0gdG9hc3QuaWQgfHwgY3J5cHRvLnJhbmRvbVVVSUQoKVxyXG5cclxuICAgIHNldFRvYXN0cygocHJldikgPT4ge1xyXG4gICAgICAvLyBDaGVjayBpZiB3ZSBuZWVkIHRvIGxpbWl0IHRoZSBudW1iZXIgb2YgdG9hc3RzXHJcbiAgICAgIGNvbnN0IG5ld1RvYXN0cyA9IFt7IC4uLnRvYXN0LCBpZCB9LCAuLi5wcmV2XVxyXG4gICAgICByZXR1cm4gbmV3VG9hc3RzLnNsaWNlKDAsIDUpIC8vIExpbWl0IHRvIDUgdG9hc3RzXHJcbiAgICB9KVxyXG5cclxuICAgIHJldHVybiBpZFxyXG4gIH1cclxuXHJcbiAgY29uc3QgdXBkYXRlVG9hc3QgPSAodG9hc3Q6IFRvYXN0VHlwZSkgPT4ge1xyXG4gICAgaWYgKCF0b2FzdC5pZCkgcmV0dXJuXHJcblxyXG4gICAgc2V0VG9hc3RzKChwcmV2KSA9PiBwcmV2Lm1hcCgodCkgPT4gKHQuaWQgPT09IHRvYXN0LmlkID8geyAuLi50LCAuLi50b2FzdCB9IDogdCkpKVxyXG4gIH1cclxuXHJcbiAgY29uc3QgZGlzbWlzc1RvYXN0ID0gKHRvYXN0SWQ6IHN0cmluZykgPT4ge1xyXG4gICAgc2V0VG9hc3RzKChwcmV2KSA9PiBwcmV2Lm1hcCgodCkgPT4gKHQuaWQgPT09IHRvYXN0SWQgPyB7IC4uLnQsIG9wZW46IGZhbHNlIH0gOiB0KSkpXHJcbiAgfVxyXG5cclxuICBjb25zdCByZW1vdmVUb2FzdCA9ICh0b2FzdElkOiBzdHJpbmcpID0+IHtcclxuICAgIHNldFRvYXN0cygocHJldikgPT4gcHJldi5maWx0ZXIoKHQpID0+IHQuaWQgIT09IHRvYXN0SWQpKVxyXG4gIH1cclxuXHJcbiAgLy8gTWVtb2l6ZSB0aGUgY29udGV4dCB2YWx1ZSB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlLXJlbmRlcnNcclxuICBjb25zdCBjb250ZXh0VmFsdWUgPSB1c2VNZW1vKFxyXG4gICAgKCkgPT4gKHtcclxuICAgICAgdG9hc3RzLFxyXG4gICAgICBhZGRUb2FzdCxcclxuICAgICAgdXBkYXRlVG9hc3QsXHJcbiAgICAgIGRpc21pc3NUb2FzdCxcclxuICAgICAgcmVtb3ZlVG9hc3QsXHJcbiAgICB9KSxcclxuICAgIFt0b2FzdHNdLFxyXG4gIClcclxuXHJcbiAgcmV0dXJuIDxUb2FzdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e2NvbnRleHRWYWx1ZX0+e2NoaWxkcmVufTwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZU1lbW8iLCJUb2FzdENvbnRleHQiLCJUb2FzdFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ0b2FzdHMiLCJzZXRUb2FzdHMiLCJhZGRUb2FzdCIsInRvYXN0IiwiaWQiLCJjcnlwdG8iLCJyYW5kb21VVUlEIiwicHJldiIsIm5ld1RvYXN0cyIsInNsaWNlIiwidXBkYXRlVG9hc3QiLCJtYXAiLCJ0IiwiZGlzbWlzc1RvYXN0IiwidG9hc3RJZCIsIm9wZW4iLCJyZW1vdmVUb2FzdCIsImZpbHRlciIsImNvbnRleHRWYWx1ZSIsIlByb3ZpZGVyIiwidmFsdWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full', {\n    variants: {\n        variant: {\n            default: 'border bg-background text-foreground',\n            destructive: 'destructive group border-destructive bg-destructive text-destructive-foreground'\n        }\n    },\n    defaultVariants: {\n        variant: 'default'\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600', className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 83,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 74,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm font-semibold', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 92,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm opacity-90', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 100,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toaster.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/toaster.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./src/components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts, dismiss } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(({ id, title, description, action, ...props })=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 31\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {\n                            onClick: ()=>id && dismiss(id)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id || crypto.randomUUID(), true, {\n                    fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/use-toast.ts":
/*!****************************************!*\
  !*** ./src/components/ui/use-toast.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastContext: () => (/* binding */ ToastContext),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ToastContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    toasts: [],\n    addToast: ()=>'',\n    updateToast: ()=>{},\n    dismissToast: ()=>{},\n    removeToast: ()=>{}\n});\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ToastContext);\n    if (context === undefined) {\n        throw new Error('useToast must be used within a ToastProvider');\n    }\n    return {\n        toast: (props)=>context.addToast(props),\n        dismiss: (toastId)=>context.dismissToast(toastId),\n        toasts: context.toasts\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/api/client.ts\");\n\n// API endpoints for authentication\nconst endpoints = {\n    login: 'api/authen/login',\n    register: 'api/authen/register',\n    user: 'api/authen/user',\n    refreshToken: 'api/authen/refresh-token',\n    revokeToken: 'api/authen/revoke-token',\n    forgotPassword: 'api/authen/forgot-password',\n    resetPassword: 'api/authen/reset-password',\n    changePassword: 'api/users/change-password',\n    resendVerification: 'api/email/resend',\n    verifyEmail: 'api/email/verify'\n};\n/**\r\n * Sanitizes and formats a token string\r\n */ function sanitizeToken(token) {\n    let sanitizedToken = token.trim();\n    // Remove spaces\n    if (sanitizedToken.includes(' ')) {\n        console.warn('Token contains spaces - cleaning up');\n        sanitizedToken = sanitizedToken.replace(/\\s/g, '');\n    }\n    // Try URL decoding if needed\n    try {\n        if (sanitizedToken.includes('%')) {\n            console.log('Token appears to be URL encoded - decoding');\n            return decodeURIComponent(sanitizedToken);\n        }\n    } catch (e) {\n        console.warn('Error trying to decode token:', e);\n    }\n    return sanitizedToken;\n}\n/**\r\n * Logs API error details and throws appropriate error\r\n */ function handleResetPasswordError(error) {\n    console.error('Reset password request failed with error:', error);\n    const apiError = error;\n    // Log error details\n    if (apiError?.status) console.error('Status code:', apiError.status);\n    if (apiError?.message) console.error('Error message:', apiError.message);\n    if (apiError?.details) console.error('Error details:', apiError.details);\n    if (apiError?.errors) console.error('Validation errors:', apiError.errors);\n    // Handle validation errors\n    if (apiError?.errors) {\n        const validationErrors = formatValidationErrors(apiError.errors);\n        throw new Error(`Password reset failed with validation errors: ${validationErrors}`);\n    }\n    if (apiError?.message) throw apiError;\n    throw new Error('Password reset failed. Please try again.');\n}\n/**\r\n * Formats validation errors into a readable string\r\n */ function formatValidationErrors(errors) {\n    return Object.entries(errors).map(([field, messages])=>`${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`).join('; ');\n}\n/**\r\n * Authentication API service\r\n * Handles all authentication-related API calls\r\n */ const authApi = {\n    /**\r\n   * Log in an existing user\r\n   * @param data Login credentials\r\n   * @returns Authentication response with token and user data\r\n   */ login: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.login, data, {\n            credentials: 'include'\n        });\n        return response;\n    },\n    /**\r\n   * Register a new user\r\n   * @param data Registration data including email, password, name\r\n   * @returns Response with user data\r\n   */ register: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.register, data, {\n            credentials: 'include'\n        });\n        return response.user;\n    },\n    /**\r\n   * Get the current user profile\r\n   * @returns User profile data\r\n   */ getCurrentUser: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.api.get(endpoints.user);\n        return response;\n    },\n    /**\r\n   * Refresh the access token using the HTTP-only cookie refresh token\r\n   * @returns New authentication tokens and user data\r\n   */ refreshToken: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.refreshToken, {}, {\n            credentials: 'include'\n        });\n        return response;\n    },\n    /**\r\n   * Log out the current user by revoking the refresh token\r\n   */ logout: async ()=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.revokeToken, {}, {\n            credentials: 'include'\n        });\n    },\n    /**\r\n   * Send a forgot password email\r\n   * @param data Email address for password reset\r\n   */ forgotPassword: async (data)=>{\n        try {\n            console.log('Sending forgot password request for email:', data.email);\n            // Send the request directly to the forgot password endpoint\n            await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.forgotPassword, data);\n            console.log('Forgot password request sent successfully');\n        } catch (error) {\n            console.error('Forgot password request failed:', error);\n            throw error;\n        }\n    },\n    /**\r\n   * Reset password with a token\r\n   * @param data Token and new password\r\n   */ resetPassword: async (data)=>{\n        try {\n            // Prepare sanitized data\n            const sanitizedData = {\n                email: data.email.trim(),\n                token: sanitizeToken(data.token.trim()),\n                newPassword: data.newPassword,\n                confirmPassword: data.confirmPassword\n            };\n            // Log request data (masked for security)\n            console.log('Sending reset password request with data:', {\n                email: sanitizedData.email,\n                tokenLength: sanitizedData.token.length,\n                tokenPrefix: sanitizedData.token.substring(0, 10) + '...',\n                newPassword: sanitizedData.newPassword ? '******' : 'MISSING',\n                confirmPassword: sanitizedData.confirmPassword ? '******' : 'MISSING'\n            });\n            // Debug log the full structure (with passwords masked)\n            console.log('Request payload structure:', JSON.stringify({\n                ...sanitizedData,\n                newPassword: '*****',\n                confirmPassword: '*****'\n            }, null, 2));\n            // Send the request\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.resetPassword, sanitizedData, {\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            console.log('Password reset request successful', response);\n        } catch (error) {\n            handleResetPasswordError(error);\n        }\n    },\n    /**\r\n   * Change the current user's password\r\n   * @param data Current and new password\r\n   */ changePassword: async (data)=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.changePassword, data);\n    },\n    /**\r\n   * Resend verification email to a registered user\r\n   * @param email User's email address\r\n   */ resendVerificationEmail: async (email)=>{\n        await _client__WEBPACK_IMPORTED_MODULE_0__.api.post(endpoints.resendVerification, {\n            email\n        });\n    },\n    /**\r\n   * Verify email with token\r\n   * @param token Email verification token\r\n   * @returns Success status\r\n   */ verifyEmail: async (token)=>{\n        try {\n            await _client__WEBPACK_IMPORTED_MODULE_0__.api.get(`${endpoints.verifyEmail}?token=${token}`);\n            return true;\n        } catch (error) {\n            console.error('Verification failed', error);\n            return false;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/client.ts":
/*!*******************************!*\
  !*** ./src/lib/api/client.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/token-storage */ \"(ssr)/./src/lib/auth/token-storage.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/**\r\n * API client for the OpenAutomate backend\r\n * Uses the environment variable NEXT_PUBLIC_API_URL for the API base URL\r\n */ // Import browser-safe localStorage utility\n\n\n// Default request headers from configuration\nconst defaultHeaders = _lib_config__WEBPACK_IMPORTED_MODULE_1__.config.api.defaultHeaders;\n// Keep track of if we're currently refreshing the token\nlet isRefreshing = false;\n// Queue of requests waiting for token refresh\nlet failedQueue = [];\n// Helper to process the queue of pending requests\nconst processQueue = (error, token = null)=>{\n    failedQueue.forEach((promise)=>{\n        if (error) {\n            promise.reject(error);\n        } else {\n            promise.resolve(token);\n        }\n    });\n    failedQueue = [];\n};\n/**\r\n * Create API error object from response\r\n */ const createApiError = async (response)=>{\n    const errorData = {\n        message: response.statusText,\n        status: response.status\n    };\n    try {\n        // Try to parse error details from response\n        const errorBody = await response.json();\n        if (errorBody.message) {\n            errorData.message = errorBody.message;\n            errorData.details = errorBody.message;\n        } else {\n            errorData.details = JSON.stringify(errorBody);\n        }\n    } catch  {\n        // If parsing fails, use status text\n        errorData.details = response.statusText;\n    }\n    return errorData;\n};\n/**\r\n * Notify the app about token expiration\r\n */ const notifyTokenExpired = ()=>{\n    if (false) {}\n};\n/**\r\n * Handle network errors\r\n */ const handleNetworkError = (error)=>{\n    // If it's already an ApiError (from our code), just rethrow it\n    if (error && typeof error === 'object' && 'status' in error && 'message' in error) {\n        throw error;\n    }\n    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n        const apiError = {\n            message: 'Network error. Please check your connection.',\n            status: 0,\n            details: error.message\n        };\n        throw apiError;\n    }\n    // For Error objects, preserve the message\n    if (error instanceof Error) {\n        const apiError = {\n            message: error.message,\n            status: 0,\n            details: error.stack\n        };\n        throw apiError;\n    }\n    throw error;\n};\n/**\r\n * Process successful response\r\n */ const processSuccessResponse = async (response)=>{\n    // Return empty object for 204 No Content responses\n    if (response.status === 204) {\n        return {};\n    }\n    // Parse JSON response\n    return await response.json();\n};\n/**\r\n * Attempt to refresh the authentication token\r\n */ const refreshToken = async ()=>{\n    // If already refreshing, wait for the current refresh to complete\n    if (isRefreshing) {\n        return new Promise((resolve, reject)=>{\n            failedQueue.push({\n                resolve,\n                reject\n            });\n        });\n    }\n    isRefreshing = true;\n    try {\n        const response = await fetchApi('api/authen/refresh-token', {\n            method: 'POST',\n            credentials: 'include'\n        });\n        const newToken = response.token;\n        (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_0__.setAuthToken)(newToken);\n        processQueue(null, newToken);\n        return newToken;\n    } catch (error) {\n        processQueue(error);\n        throw error;\n    } finally{\n        isRefreshing = false;\n    }\n};\n/**\r\n * Handle 401 unauthorized response\r\n */ const handle401Response = async (endpoint, url, options, headers, data)=>{\n    // Skip token refresh for login and refresh-token endpoints\n    if (endpoint.includes('refresh-token') || endpoint.includes('login')) {\n        notifyTokenExpired();\n        return null;\n    }\n    try {\n        const newToken = await refreshToken();\n        if (!newToken) return null;\n        // Prepare the retry request properly with the new token\n        const retryHeaders = prepareHeaders(options, data);\n        retryHeaders.Authorization = `Bearer ${newToken}`;\n        // Use the same body preparation logic to avoid ArrayBuffer issues\n        const { body } = prepareRequestBody(data);\n        const retriedResponse = await fetch(url, {\n            ...options,\n            body,\n            headers: retryHeaders,\n            credentials: 'include'\n        });\n        if (retriedResponse.ok) {\n            return processSuccessResponse(retriedResponse);\n        }\n        return null;\n    } catch (refreshError) {\n        notifyTokenExpired();\n        console.error('Token refresh failed:', refreshError);\n        return null;\n    }\n};\n/**\r\n * Get full API URL\r\n */ const getFullUrl = (endpoint)=>{\n    if (endpoint.startsWith('http')) {\n        return endpoint;\n    }\n    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;\n    return `${_lib_config__WEBPACK_IMPORTED_MODULE_1__.config.api.baseUrl}/${cleanEndpoint}`;\n};\n/**\r\n * Prepare request body and headers for different data types\r\n */ const prepareRequestBody = (data)=>{\n    if (!data) {\n        return {\n            body: undefined,\n            headers: {}\n        };\n    }\n    // Handle FormData - don't stringify and don't set any headers (browser will handle)\n    if (data instanceof FormData) {\n        return {\n            body: data,\n            headers: {} // No headers needed, browser will set multipart/form-data with boundary\n        };\n    }\n    // Handle regular objects - stringify as JSON and set Content-Type\n    return {\n        body: JSON.stringify(data),\n        headers: {\n            'Content-Type': 'application/json'\n        }\n    };\n};\n/**\r\n * Prepare request headers\r\n */ const prepareHeaders = (options, data)=>{\n    // Start with default headers, but exclude Content-Type if we're sending FormData\n    const shouldExcludeContentType = data instanceof FormData;\n    const baseHeaders = shouldExcludeContentType ? {\n        Accept: defaultHeaders.Accept\n    } // Only include Accept header for FormData\n     : {\n        ...defaultHeaders\n    };\n    const headers = {\n        ...baseHeaders,\n        ...options.headers\n    };\n    if (!headers.Authorization) {\n        const token = (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        if (token) {\n            headers.Authorization = `Bearer ${token}`;\n        }\n    }\n    return headers;\n};\n/**\r\n * Generic function to make API requests\r\n */ async function fetchApi(endpoint, options = {}, data) {\n    const url = getFullUrl(endpoint);\n    const headers = prepareHeaders(options, data);\n    try {\n        const response = await fetch(url, {\n            ...options,\n            headers,\n            credentials: 'include'\n        });\n        // Handle successful responses\n        if (response.ok) {\n            return processSuccessResponse(response);\n        }\n        // Handle 401 Unauthorized responses\n        if (response.status === 401) {\n            const refreshResult = await handle401Response(endpoint, url, options, headers, data);\n            if (refreshResult) {\n                return refreshResult;\n            }\n        }\n        // For all other error responses, create and throw an API error\n        const errorData = await createApiError(response);\n        // Log error for debugging\n        console.error(`API Error [${response.status}]:`, errorData.message);\n        throw errorData;\n    } catch (error) {\n        if (error && typeof error === 'object' && 'status' in error && 'message' in error) {\n            // Nếu đã là ApiError, trả về ngay\n            throw error;\n        }\n        return handleNetworkError(error);\n    }\n}\n/**\r\n * HTTP request methods with proper typing\r\n */ const api = {\n    get: (endpoint, options)=>fetchApi(endpoint, {\n            ...options,\n            method: 'GET'\n        }),\n    post: (endpoint, data, options)=>{\n        const { body, headers: bodyHeaders } = prepareRequestBody(data);\n        return fetchApi(endpoint, {\n            ...options,\n            method: 'POST',\n            body,\n            headers: {\n                ...bodyHeaders,\n                ...options?.headers\n            }\n        }, data);\n    },\n    put: (endpoint, data, options)=>{\n        const { body, headers: bodyHeaders } = prepareRequestBody(data);\n        return fetchApi(endpoint, {\n            ...options,\n            method: 'PUT',\n            body,\n            headers: {\n                ...bodyHeaders,\n                ...options?.headers\n            }\n        }, data);\n    },\n    patch: (endpoint, data, options)=>{\n        const { body, headers: bodyHeaders } = prepareRequestBody(data);\n        return fetchApi(endpoint, {\n            ...options,\n            method: 'PATCH',\n            body,\n            headers: {\n                ...bodyHeaders,\n                ...options?.headers\n            }\n        }, data);\n    },\n    delete: (endpoint, options)=>fetchApi(endpoint, {\n            ...options,\n            method: 'DELETE'\n        })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth/token-storage.ts":
/*!***************************************!*\
  !*** ./src/lib/auth/token-storage.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   setAuthToken: () => (/* binding */ setAuthToken),\n/* harmony export */   setUser: () => (/* binding */ setUser)\n/* harmony export */ });\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/**\r\n * Token storage utilities\r\n * Implements a memory-first approach with localStorage fallback for resilience\r\n */ // Import user type\n\n// Storage keys from config\nconst TOKEN_KEY = _lib_config__WEBPACK_IMPORTED_MODULE_0__.config.auth.tokenStorageKey;\nconst USER_KEY = _lib_config__WEBPACK_IMPORTED_MODULE_0__.config.auth.userStorageKey;\n// In-memory storage (more secure than localStorage)\nlet inMemoryToken = null;\nlet inMemoryUser = null;\n/**\r\n * Gets the authentication token, prioritizing in-memory storage\r\n */ const getAuthToken = ()=>{\n    // First check in-memory token (more secure, not persisted)\n    if (inMemoryToken) {\n        return inMemoryToken;\n    }\n    // Then check localStorage as fallback (only on client-side)\n    if (false) {}\n    return null;\n};\n/**\r\n * Sets the authentication token, prioritizing in-memory storage\r\n */ const setAuthToken = (token)=>{\n    // Always update in-memory token\n    inMemoryToken = token;\n    // Update localStorage for persistence through refreshes\n    if (false) {}\n};\n/**\r\n * Gets the stored user data\r\n */ const getUser = ()=>{\n    // First check in-memory\n    if (inMemoryUser) {\n        return inMemoryUser;\n    }\n    // Then check localStorage as fallback\n    if (false) {}\n    return null;\n};\n/**\r\n * Sets the user data\r\n */ const setUser = (user)=>{\n    // Update in-memory\n    inMemoryUser = user;\n    // Update localStorage\n    if (false) {}\n};\n/**\r\n * Clears all authentication data\r\n */ const clearAuthData = ()=>{\n    // Clear memory\n    inMemoryToken = null;\n    inMemoryUser = null;\n    // Clear localStorage\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth/token-storage.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/**\r\n * Central configuration settings for the application\r\n * Makes configuration values accessible throughout the app\r\n */ const config = {\n    /**\r\n   * Application information\r\n   */ app: {\n        /**\r\n     * Application name\r\n     */ name: 'OpenAutomate Orchestrator',\n        /**\r\n     * Application URL - different for dev and production\r\n     */ url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n        /**\r\n     * Production domain - used for cookies in production\r\n     */ domain:  false ? 0 : 'localhost'\n    },\n    /**\r\n   * API configuration\r\n   */ api: {\n        /**\r\n     * Base URL for API requests\r\n     */ baseUrl:  false ? 0 : \"http://localhost:5252\" || 0,\n        /**\r\n     * Default headers for API requests\r\n     */ defaultHeaders: {\n            'Content-Type': 'application/json',\n            Accept: 'application/json'\n        }\n    },\n    /**\r\n   * Authentication configuration\r\n   */ auth: {\n        /**\r\n     * Token refresh interval in milliseconds (14 minutes)\r\n     */ tokenRefreshInterval: 14 * 60 * 1000,\n        /**\r\n     * Token storage key in localStorage\r\n     */ tokenStorageKey: 'auth_token',\n        /**\r\n     * User data storage key in localStorage\r\n     */ userStorageKey: 'user_data'\n    },\n    /**\r\n   * URL paths\r\n   */ paths: {\n        /**\r\n     * Authentication-related paths\r\n     */ auth: {\n            login: '/login',\n            register: '/register',\n            forgotPassword: '/forgot-password',\n            resetPassword: '/reset-password',\n            verificationPending: '/verification-pending',\n            emailVerified: '/email-verified',\n            verifyEmail: '/verify-email',\n            organizationSelector: '/tenant-selector'\n        },\n        /**\r\n     * Default redirect after login\r\n     */ defaultRedirect: '/tenant-selector'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   delay: () => (/* binding */ delay),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   snakeToCamel: () => (/* binding */ snakeToCamel),\n/* harmony export */   truncateString: () => (/* binding */ truncateString)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Combines multiple class names or class value arrays and merges them with Tailwind specific merging\r\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Format a date object to a readable string\r\n */ function formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        month: 'long',\n        day: 'numeric',\n        year: 'numeric'\n    }).format(date);\n}\n/**\r\n * Format a currency value\r\n */ function formatCurrency(amount) {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: 'USD'\n    }).format(amount);\n}\n/**\r\n * Truncate a string to a specified length\r\n */ function truncateString(str, num) {\n    if (str.length <= num) {\n        return str;\n    }\n    return str.slice(0, num) + '...';\n}\n/**\r\n * Delay execution (useful for debouncing)\r\n */ function delay(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\r\n * Convert snake_case to camelCase\r\n */ function snakeToCamel(str) {\n    return str.replace(/([-_][a-z])/g, (group)=>group.toUpperCase().replace('-', '').replace('_', ''));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFeEM7O0NBRUMsR0FDTSxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFFQTs7Q0FFQyxHQUNNLFNBQVNDLFdBQVdDLElBQVU7SUFDbkMsT0FBTyxJQUFJQyxLQUFLQyxjQUFjLENBQUMsU0FBUztRQUN0Q0MsT0FBTztRQUNQQyxLQUFLO1FBQ0xDLE1BQU07SUFDUixHQUFHQyxNQUFNLENBQUNOO0FBQ1o7QUFFQTs7Q0FFQyxHQUNNLFNBQVNPLGVBQWVDLE1BQWM7SUFDM0MsT0FBTyxJQUFJUCxLQUFLUSxZQUFZLENBQUMsU0FBUztRQUNwQ0MsT0FBTztRQUNQQyxVQUFVO0lBQ1osR0FBR0wsTUFBTSxDQUFDRTtBQUNaO0FBRUE7O0NBRUMsR0FDTSxTQUFTSSxlQUFlQyxHQUFXLEVBQUVDLEdBQVc7SUFDckQsSUFBSUQsSUFBSUUsTUFBTSxJQUFJRCxLQUFLO1FBQ3JCLE9BQU9EO0lBQ1Q7SUFDQSxPQUFPQSxJQUFJRyxLQUFLLENBQUMsR0FBR0YsT0FBTztBQUM3QjtBQUVBOztDQUVDLEdBQ00sU0FBU0csTUFBTUMsRUFBVTtJQUM5QixPQUFPLElBQUlDLFFBQVEsQ0FBQ0MsVUFBWUMsV0FBV0QsU0FBU0Y7QUFDdEQ7QUFFQTs7Q0FFQyxHQUNNLFNBQVNJLGFBQWFULEdBQVc7SUFDdEMsT0FBT0EsSUFBSVUsT0FBTyxDQUFDLGdCQUFnQixDQUFDQyxRQUNsQ0EsTUFBTUMsV0FBVyxHQUFHRixPQUFPLENBQUMsS0FBSyxJQUFJQSxPQUFPLENBQUMsS0FBSztBQUV0RCIsInNvdXJjZXMiOlsiRDpcXENhcHN0b25lUHJvamVjdFxcT3BlbkF1dG9tYXRlLkZyb250ZW5kXFxzcmNcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSAnY2xzeCdcclxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJ1xyXG5cclxuLyoqXHJcbiAqIENvbWJpbmVzIG11bHRpcGxlIGNsYXNzIG5hbWVzIG9yIGNsYXNzIHZhbHVlIGFycmF5cyBhbmQgbWVyZ2VzIHRoZW0gd2l0aCBUYWlsd2luZCBzcGVjaWZpYyBtZXJnaW5nXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcclxuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBGb3JtYXQgYSBkYXRlIG9iamVjdCB0byBhIHJlYWRhYmxlIHN0cmluZ1xyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZTogRGF0ZSk6IHN0cmluZyB7XHJcbiAgcmV0dXJuIG5ldyBJbnRsLkRhdGVUaW1lRm9ybWF0KCdlbi1VUycsIHtcclxuICAgIG1vbnRoOiAnbG9uZycsXHJcbiAgICBkYXk6ICdudW1lcmljJyxcclxuICAgIHllYXI6ICdudW1lcmljJyxcclxuICB9KS5mb3JtYXQoZGF0ZSlcclxufVxyXG5cclxuLyoqXHJcbiAqIEZvcm1hdCBhIGN1cnJlbmN5IHZhbHVlXHJcbiAqL1xyXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0Q3VycmVuY3koYW1vdW50OiBudW1iZXIpOiBzdHJpbmcge1xyXG4gIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ2VuLVVTJywge1xyXG4gICAgc3R5bGU6ICdjdXJyZW5jeScsXHJcbiAgICBjdXJyZW5jeTogJ1VTRCcsXHJcbiAgfSkuZm9ybWF0KGFtb3VudClcclxufVxyXG5cclxuLyoqXHJcbiAqIFRydW5jYXRlIGEgc3RyaW5nIHRvIGEgc3BlY2lmaWVkIGxlbmd0aFxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIHRydW5jYXRlU3RyaW5nKHN0cjogc3RyaW5nLCBudW06IG51bWJlcik6IHN0cmluZyB7XHJcbiAgaWYgKHN0ci5sZW5ndGggPD0gbnVtKSB7XHJcbiAgICByZXR1cm4gc3RyXHJcbiAgfVxyXG4gIHJldHVybiBzdHIuc2xpY2UoMCwgbnVtKSArICcuLi4nXHJcbn1cclxuXHJcbi8qKlxyXG4gKiBEZWxheSBleGVjdXRpb24gKHVzZWZ1bCBmb3IgZGVib3VuY2luZylcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBkZWxheShtczogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiB7XHJcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIG1zKSlcclxufVxyXG5cclxuLyoqXHJcbiAqIENvbnZlcnQgc25ha2VfY2FzZSB0byBjYW1lbENhc2VcclxuICovXHJcbmV4cG9ydCBmdW5jdGlvbiBzbmFrZVRvQ2FtZWwoc3RyOiBzdHJpbmcpOiBzdHJpbmcge1xyXG4gIHJldHVybiBzdHIucmVwbGFjZSgvKFstX11bYS16XSkvZywgKGdyb3VwKSA9PlxyXG4gICAgZ3JvdXAudG9VcHBlckNhc2UoKS5yZXBsYWNlKCctJywgJycpLnJlcGxhY2UoJ18nLCAnJyksXHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwiZm9ybWF0RGF0ZSIsImRhdGUiLCJJbnRsIiwiRGF0ZVRpbWVGb3JtYXQiLCJtb250aCIsImRheSIsInllYXIiLCJmb3JtYXQiLCJmb3JtYXRDdXJyZW5jeSIsImFtb3VudCIsIk51bWJlckZvcm1hdCIsInN0eWxlIiwiY3VycmVuY3kiLCJ0cnVuY2F0ZVN0cmluZyIsInN0ciIsIm51bSIsImxlbmd0aCIsInNsaWNlIiwiZGVsYXkiLCJtcyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInNuYWtlVG9DYW1lbCIsInJlcGxhY2UiLCJncm91cCIsInRvVXBwZXJDYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/auth-logger.ts":
/*!**************************************!*\
  !*** ./src/lib/utils/auth-logger.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logLoginSuccess: () => (/* binding */ logLoginSuccess),\n/* harmony export */   logLogout: () => (/* binding */ logLogout)\n/* harmony export */ });\n/* harmony import */ var _logger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logger */ \"(ssr)/./src/lib/utils/logger.ts\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/auth */ \"(ssr)/./src/types/auth.ts\");\n/**\r\n * Auth Logger - Specialized logging utility for authentication events\r\n * Provides nicely formatted console output specifically for authentication events\r\n */ \n\n/**\r\n * Gets a user-friendly display name for a system role\r\n * @param role The system role enum value\r\n * @returns A readable string representation of the role\r\n */ const getSystemRoleName = (role)=>{\n    return _types_auth__WEBPACK_IMPORTED_MODULE_1__.SystemRole[role] || 'Unknown';\n};\n/**\r\n * Logs a user login success event with formatted console output\r\n *\r\n * @param user The authenticated user information\r\n * @param token Token information (boolean indicators only for security)\r\n */ const logLoginSuccess = (user, token)=>{\n    if (false) {}\n    // Prepare styled console output\n    console.group('%c🔐 LOGIN SUCCESSFUL', 'color: #9b59b6; font-weight: bold; font-size: 14px; background: #f8f9fa; padding: 5px 10px; border-radius: 3px;');\n    // Display user info in a styled box\n    console.log('%cUser Information', 'color: #2c3e50; font-weight: bold; font-size: 12px; border-bottom: 1px solid #ddd;');\n    console.table({\n        id: user.id,\n        email: user.email,\n        name: `${user.firstName} ${user.lastName}`,\n        systemRole: user.systemRole !== undefined ? `${getSystemRoleName(user.systemRole)} (${user.systemRole})` : 'Not assigned'\n    });\n    // Display auth details\n    console.log('%cAuthentication Details', 'color: #2c3e50; font-weight: bold; font-size: 12px; border-bottom: 1px solid #ddd;');\n    console.log('%c✓ Access Token: ' + (token.received ? 'Received' : 'Not received'), `color: ${token.received ? '#27ae60' : '#e74c3c'}; font-weight: bold;`);\n    console.log('%c✓ Refresh Token: ' + (token.refreshTokenReceived ? 'Received' : 'Not received'), `color: ${token.refreshTokenReceived ? '#27ae60' : '#e74c3c'}; font-weight: bold;`);\n    if (token.expiration) {\n        const expirationDate = new Date(token.expiration);\n        const now = new Date();\n        const timeDiff = expirationDate.getTime() - now.getTime();\n        const hoursDiff = Math.round(timeDiff / (1000 * 60 * 60));\n        console.log(`%c✓ Token Expires: In approximately ${hoursDiff} hours (${expirationDate.toLocaleString()})`, 'color: #f39c12; font-weight: bold;');\n    }\n    // Add timestamp\n    console.log('%cTimestamp: ' + new Date().toLocaleString(), 'color: #7f8c8d; font-style: italic;');\n    console.groupEnd();\n};\n/**\r\n * Logs a user logout event\r\n */ const logLogout = ()=>{\n    _logger__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auth('User Logged Out', {\n        timestamp: new Date().toLocaleString(),\n        status: 'success'\n    });\n};\n// Export as default object\nconst authLogger = {\n    loginSuccess: logLoginSuccess,\n    logout: logLogout\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authLogger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/auth-logger.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/logger.ts":
/*!*********************************!*\
  !*** ./src/lib/utils/logger.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   logAuth: () => (/* binding */ logAuth),\n/* harmony export */   logDivider: () => (/* binding */ logDivider),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logSuccess: () => (/* binding */ logSuccess),\n/* harmony export */   logWarning: () => (/* binding */ logWarning)\n/* harmony export */ });\n/**\r\n * Logger utility with styled console output for development purposes\r\n */ // Style configuration for different log types\nconst styles = {\n    info: 'color: #3498db; font-weight: bold;',\n    success: 'color: #2ecc71; font-weight: bold;',\n    warning: 'color: #f39c12; font-weight: bold;',\n    error: 'color: #e74c3c; font-weight: bold;',\n    auth: 'color: #9b59b6; font-weight: bold;',\n    default: 'color: #34495e; font-weight: normal;',\n    group: 'color: #2c3e50; font-weight: bold; font-size: 1.1em;',\n    object: 'color: #7f8c8d; font-weight: normal;'\n};\n/**\r\n * Logs a message with optional styling based on log level\r\n */ const log = (message, level = 'info', ...data)=>{\n    // Only log in development environment\n    if (true) {\n        const style = styles[level] || styles.default;\n        console.log(`%c${message}`, style, ...data);\n    }\n};\n/**\r\n * Logs a success message\r\n */ const logSuccess = (message, ...data)=>{\n    log(message, 'success', ...data);\n};\n/**\r\n * Logs an error message\r\n */ const logError = (message, ...data)=>{\n    log(message, 'error', ...data);\n};\n/**\r\n * Logs a warning message\r\n */ const logWarning = (message, ...data)=>{\n    log(message, 'warning', ...data);\n};\n/**\r\n * Logs authentication-related messages with special styling\r\n */ const logAuth = (message, data)=>{\n    if (true) {\n        console.group(`%c🔐 ${message}`, styles.group);\n        if (data) {\n            if (typeof data === 'object') {\n                console.log('%cDetails:', styles.object);\n                console.table(data);\n            } else {\n                console.log(data);\n            }\n        }\n        console.groupEnd();\n    }\n};\n/**\r\n * Creates a styled divider in the console\r\n */ const logDivider = (label)=>{\n    if (true) {\n        if (label) {\n            console.log(`%c---------------- ${label} ----------------`, styles.info);\n        } else {\n            console.log('%c----------------------------------------', styles.info);\n        }\n    }\n};\n// Export as default object\nconst logger = {\n    log,\n    success: logSuccess,\n    error: logError,\n    warning: logWarning,\n    auth: logAuth,\n    divider: logDivider\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/logger.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/auth-provider.tsx":
/*!*****************************************!*\
  !*** ./src/providers/auth-provider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthContext: () => (/* binding */ AuthContext),\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/auth */ \"(ssr)/./src/lib/api/auth.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _types_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/types/auth */ \"(ssr)/./src/types/auth.ts\");\n/* harmony import */ var _lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth/token-storage */ \"(ssr)/./src/lib/auth/token-storage.ts\");\n/* harmony import */ var _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/logger */ \"(ssr)/./src/lib/utils/logger.ts\");\n/* harmony import */ var _lib_utils_auth_logger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/auth-logger */ \"(ssr)/./src/lib/utils/auth-logger.ts\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/config */ \"(ssr)/./src/lib/config.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthContext,AuthProvider,useAuth auto */ \n\n\n\n\n\n\n\n\n// Create the auth context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Token refresh interval from config\nconst TOKEN_REFRESH_INTERVAL = _lib_config__WEBPACK_IMPORTED_MODULE_8__.config.auth.tokenRefreshInterval;\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Computed property for system admin status\n    const isSystemAdmin = user?.systemRole === _types_auth__WEBPACK_IMPORTED_MODULE_4__.SystemRole.Admin;\n    // Refresh token implementation\n    const refreshToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshToken]\": async ()=>{\n            try {\n                const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.refreshToken();\n                // Update token in storage\n                (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.setAuthToken)(response.token);\n                // Update user if it exists in the response\n                const userToSet = response.user || {\n                    id: response.id,\n                    email: response.email,\n                    firstName: response.firstName,\n                    lastName: response.lastName,\n                    systemRole: response.systemRole || _types_auth__WEBPACK_IMPORTED_MODULE_4__.SystemRole.User\n                };\n                // Update in storage and local state\n                (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.setUser)(userToSet);\n                setUser(userToSet);\n                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('Token refreshed successfully');\n                return true;\n            } catch (err) {\n                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Token refresh failed:', err);\n                // Clear auth data on refresh failure\n                (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.clearAuthData)();\n                setUser(null);\n                return false;\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshToken]\"], []);\n    // Set up token refresh on interval and tab focus\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            let refreshInterval = null;\n            // Only set up refresh if user is authenticated\n            if (user) {\n                // Set up interval for token refresh\n                refreshInterval = setInterval({\n                    \"AuthProvider.useEffect\": ()=>{\n                        refreshToken();\n                    }\n                }[\"AuthProvider.useEffect\"], TOKEN_REFRESH_INTERVAL);\n                // Set up focus event for token refresh\n                const handleVisibilityChange = {\n                    \"AuthProvider.useEffect.handleVisibilityChange\": ()=>{\n                        if (document.visibilityState === 'visible') {\n                            refreshToken();\n                        }\n                    }\n                }[\"AuthProvider.useEffect.handleVisibilityChange\"];\n                document.addEventListener('visibilitychange', handleVisibilityChange);\n                return ({\n                    \"AuthProvider.useEffect\": ()=>{\n                        if (refreshInterval) clearInterval(refreshInterval);\n                        document.removeEventListener('visibilitychange', handleVisibilityChange);\n                    }\n                })[\"AuthProvider.useEffect\"];\n            }\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    if (refreshInterval) clearInterval(refreshInterval);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        user,\n        refreshToken\n    ]);\n    // Handle token expired events\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const handleTokenExpired = {\n                \"AuthProvider.useEffect.handleTokenExpired\": ()=>{\n                    _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].warning('Authentication token expired');\n                    (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.clearAuthData)();\n                    setUser(null);\n                    router.push('/login');\n                }\n            }[\"AuthProvider.useEffect.handleTokenExpired\"];\n            window.addEventListener('auth:token-expired', handleTokenExpired);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    window.removeEventListener('auth:token-expired', handleTokenExpired);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        router\n    ]);\n    // Check if user is logged in on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Check for stored token\n                        const token = (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.getAuthToken)();\n                        const userData = (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.getUser)();\n                        if (token && userData) {\n                            // If token and user exist, set user state\n                            setUser(userData);\n                            _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].log('User restored from storage', 'info', userData);\n                            // After setting user from storage, attempt to get fresh user data\n                            try {\n                                const currentUser = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.getCurrentUser();\n                                setUser(currentUser);\n                                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success('User data refreshed from API');\n                            } catch (err) {\n                                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].warning('Failed to get current user, attempting token refresh', err);\n                                // If fetching current user fails, try to refresh the token\n                                const refreshed = await refreshToken();\n                                if (!refreshed) {\n                                    (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.clearAuthData)();\n                                    setUser(null);\n                                }\n                            }\n                        }\n                    } catch (err) {\n                        _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Authentication initialization failed:', err);\n                        // Clear tokens if initialization fails\n                        (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.clearAuthData)();\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refreshToken\n    ]);\n    // Login function\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[login]\": async (data)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.login(data);\n                // Store token and user\n                (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.setAuthToken)(response.token);\n                // Use user from response or create from response fields\n                const userData = response.user || {\n                    id: response.id,\n                    email: response.email,\n                    firstName: response.firstName,\n                    lastName: response.lastName,\n                    systemRole: response.systemRole || _types_auth__WEBPACK_IMPORTED_MODULE_4__.SystemRole.User\n                };\n                // Update in storage and local state\n                (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.setUser)(userData);\n                setUser(userData);\n                // Log authentication success with standard logger\n                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(`User logged in: ${userData.email}`);\n                // Always redirect to organization selector first\n                // This lets the user choose which tenant to access\n                router.push(_lib_config__WEBPACK_IMPORTED_MODULE_8__.config.paths.defaultRedirect);\n                return userData;\n            } catch (err) {\n                // Handle API error with type safety\n                const errorMessage = typeof err === 'object' && err !== null ? err?.response?.data?.message || err?.message || 'An error occurred during login' : 'An error occurred during login';\n                setError(errorMessage);\n                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Login failed:', err);\n                throw err;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AuthProvider.useCallback[login]\"], [\n        router\n    ]);\n    // Register function\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[register]\": async (data)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.register(data);\n                // In email verification flow, we don't automatically log in the user\n                // So we don't set tokens or user data here\n                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].auth('Registration Successful', {\n                    email: data.email,\n                    message: 'Verification email sent'\n                });\n                return response;\n            } catch (err) {\n                if (err && typeof err === 'object' && 'message' in err) {\n                    const errorMessage = err.message || 'Registration failed';\n                    setError(errorMessage);\n                    _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Registration failed:', errorMessage);\n                } else {\n                    setError('Registration failed');\n                    _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Registration failed: Unknown error');\n                }\n                throw err;\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"AuthProvider.useCallback[register]\"], []);\n    // Logout function\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[logout]\": async ()=>{\n            setIsLoading(true);\n            try {\n                await _lib_api_auth__WEBPACK_IMPORTED_MODULE_2__.authApi.logout();\n                _lib_utils_auth_logger__WEBPACK_IMPORTED_MODULE_7__[\"default\"].logout();\n            } catch (err) {\n                _lib_utils_logger__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error('Logout error:', err);\n            } finally{\n                // Clear user and tokens\n                (0,_lib_auth_token_storage__WEBPACK_IMPORTED_MODULE_5__.clearAuthData)();\n                setUser(null);\n                router.push('/login');\n                setIsLoading(false);\n            }\n        }\n    }[\"AuthProvider.useCallback[logout]\"], [\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n            \"AuthProvider.useMemo\": ()=>({\n                    user,\n                    isLoading,\n                    isAuthenticated: !!user,\n                    isSystemAdmin,\n                    login,\n                    register,\n                    logout,\n                    refreshToken,\n                    error\n                })\n        }[\"AuthProvider.useMemo\"], [\n            user,\n            isLoading,\n            isSystemAdmin,\n            login,\n            register,\n            logout,\n            refreshToken,\n            error\n        ]),\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\providers\\\\auth-provider.tsx\",\n        lineNumber: 277,\n        columnNumber: 5\n    }, this);\n}\n// Hook to use auth context, deprecated - use the useAuth hook from hooks directory instead\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/locale-provider.tsx":
/*!*******************************************!*\
  !*** ./src/providers/locale-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocaleProvider: () => (/* binding */ LocaleProvider),\n/* harmony export */   useLocale: () => (/* binding */ useLocale)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LocaleProvider,useLocale auto */ \n\nconst LocaleContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst loaders = {\n    en: ()=>__webpack_require__.e(/*! import() */ \"_ssr_src_locales_en_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../locales/en.json */ \"(ssr)/./src/locales/en.json\", 19)).then((mod)=>mod.default),\n    vi: ()=>__webpack_require__.e(/*! import() */ \"_ssr_src_locales_vi_json\").then(__webpack_require__.t.bind(__webpack_require__, /*! ../locales/vi.json */ \"(ssr)/./src/locales/vi.json\", 19)).then((mod)=>mod.default)\n};\nfunction getFromMessages(messages, key) {\n    return key.split('.').reduce((obj, segment)=>typeof obj === 'object' && obj ? obj[segment] : undefined, messages) ?? key;\n}\nfunction LocaleProvider({ children }) {\n    const [locale, setLocale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocaleProvider.useEffect\": ()=>{\n            const saved = localStorage.getItem('locale');\n            if (saved) setLocale(saved);\n        }\n    }[\"LocaleProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocaleProvider.useEffect\": ()=>{\n            localStorage.setItem('locale', locale);\n            loaders[locale]().then(setMessages);\n        }\n    }[\"LocaleProvider.useEffect\"], [\n        locale\n    ]);\n    const t = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LocaleProvider.useCallback[t]\": (key)=>{\n            return getFromMessages(messages, key);\n        }\n    }[\"LocaleProvider.useCallback[t]\"], [\n        messages\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LocaleContext.Provider, {\n        value: {\n            locale,\n            setLocale,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\providers\\\\locale-provider.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\nfunction useLocale() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LocaleContext);\n    if (!ctx) throw new Error('useLocale must be used within LocaleProvider');\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/locale-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/theme-provider.tsx":
/*!******************************************!*\
  !*** ./src/providers/theme-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\CapstoneProject\\\\OpenAutomate.Frontend\\\\src\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQzREO0FBRW5GLFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsiRDpcXENhcHN0b25lUHJvamVjdFxcT3BlbkF1dG9tYXRlLkZyb250ZW5kXFxzcmNcXHByb3ZpZGVyc1xcdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXHJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyLCB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gJ25leHQtdGhlbWVzJ1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XHJcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/types/auth.ts":
/*!***************************!*\
  !*** ./src/types/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SystemRole: () => (/* binding */ SystemRole)\n/* harmony export */ });\n/**\r\n * Authentication related types\r\n */ /**\r\n * System-wide roles that determine global access levels\r\n */ var SystemRole = /*#__PURE__*/ function(SystemRole) {\n    /**\r\n   * Standard user with access limited to assigned tenant and permissions\r\n   */ SystemRole[SystemRole[\"User\"] = 0] = \"User\";\n    /**\r\n   * System administrator with full access to system-wide functionality\r\n   */ SystemRole[SystemRole[\"Admin\"] = 1] = \"Admin\";\n    return SystemRole;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/auth.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCapstoneProject%5COpenAutomate.Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();