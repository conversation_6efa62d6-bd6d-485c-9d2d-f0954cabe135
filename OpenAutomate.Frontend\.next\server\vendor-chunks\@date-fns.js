"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@date-fns";
exports.ids = ["vendor-chunks/@date-fns"];
exports.modules = {

/***/ "(ssr)/./node_modules/@date-fns/tz/constants/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@date-fns/tz/constants/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constructFromSymbol: () => (/* binding */ constructFromSymbol)\n/* harmony export */ });\n/**\n * The symbol to access the `TZDate`'s function to construct a new instance from\n * the provided value. It helps date-fns to inherit the time zone.\n */\nconst constructFromSymbol = Symbol.for(\"constructDateFrom\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L2NvbnN0YW50cy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiRDpcXENhcHN0b25lUHJvamVjdFxcT3BlbkF1dG9tYXRlLkZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBkYXRlLWZuc1xcdHpcXGNvbnN0YW50c1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaGUgc3ltYm9sIHRvIGFjY2VzcyB0aGUgYFRaRGF0ZWAncyBmdW5jdGlvbiB0byBjb25zdHJ1Y3QgYSBuZXcgaW5zdGFuY2UgZnJvbVxuICogdGhlIHByb3ZpZGVkIHZhbHVlLiBJdCBoZWxwcyBkYXRlLWZucyB0byBpbmhlcml0IHRoZSB0aW1lIHpvbmUuXG4gKi9cbmV4cG9ydCBjb25zdCBjb25zdHJ1Y3RGcm9tU3ltYm9sID0gU3ltYm9sLmZvcihcImNvbnN0cnVjdERhdGVGcm9tXCIpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/constants/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/date/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@date-fns/tz/date/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDate: () => (/* binding */ TZDate)\n/* harmony export */ });\n/* harmony import */ var _mini_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mini.js */ \"(ssr)/./node_modules/@date-fns/tz/date/mini.js\");\n\n\n/**\n * UTC date class. It maps getters and setters to corresponding UTC methods,\n * forcing all calculations in the UTC time zone.\n *\n * Combined with date-fns, it allows using the class the same way as\n * the original date class.\n *\n * This complete version provides not only getters, setters,\n * and `getTimezoneOffset`, but also the formatter functions, mirroring\n * all original `Date` functionality. Use this version when you need to format\n * a string or in an environment you don't fully control (a library).\n * For a minimal version, see `UTCDateMini`.\n */\nclass TZDate extends _mini_js__WEBPACK_IMPORTED_MODULE_0__.TZDateMini {\n  //#region static\n\n  static tz(tz, ...args) {\n    return args.length ? new TZDate(...args, tz) : new TZDate(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region representation\n\n  toISOString() {\n    const [sign, hours, minutes] = this.tzComponents();\n    const tz = `${sign}${hours}:${minutes}`;\n    return this.internal.toISOString().slice(0, -1) + tz;\n  }\n  toString() {\n    // \"Tue Aug 13 2024 07:50:19 GMT+0800 (Singapore Standard Time)\";\n    return `${this.toDateString()} ${this.toTimeString()}`;\n  }\n  toDateString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const [day, date, month, year] = this.internal.toUTCString().split(\" \");\n    // \"Tue Aug 13 2024\"\n    return `${day?.slice(0, -1) /* Remove \",\" */} ${month} ${date} ${year}`;\n  }\n  toTimeString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const time = this.internal.toUTCString().split(\" \")[4];\n    const [sign, hours, minutes] = this.tzComponents();\n    // \"07:42:23 GMT+0800 (Singapore Standard Time)\"\n    return `${time} GMT${sign}${hours}${minutes} (${tzName(this.timeZone, this)})`;\n  }\n  toLocaleString(locales, options) {\n    return Date.prototype.toLocaleString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleDateString(locales, options) {\n    return Date.prototype.toLocaleDateString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleTimeString(locales, options) {\n    return Date.prototype.toLocaleTimeString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n\n  //#endregion\n\n  //#region private\n\n  tzComponents() {\n    const offset = this.getTimezoneOffset();\n    const sign = offset > 0 ? \"-\" : \"+\";\n    const hours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, \"0\");\n    const minutes = String(Math.abs(offset) % 60).padStart(2, \"0\");\n    return [sign, hours, minutes];\n  }\n\n  //#endregion\n\n  withTimeZone(timeZone) {\n    return new TZDate(+this, timeZone);\n  }\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDate(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\nfunction tzName(tz, date) {\n  return new Intl.DateTimeFormat(\"en-GB\", {\n    timeZone: tz,\n    timeZoneName: \"long\"\n  }).format(date).slice(12);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L2RhdGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUM7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ08scUJBQXFCLGdEQUFVO0FBQ3RDOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0Esa0JBQWtCLEtBQUssRUFBRSxNQUFNLEdBQUcsUUFBUTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMscUJBQXFCLEVBQUUsb0JBQW9CO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLG9DQUFvQyxFQUFFLE9BQU8sRUFBRSxNQUFNLEVBQUUsS0FBSztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLE1BQU0sS0FBSyxLQUFLLEVBQUUsTUFBTSxFQUFFLFNBQVMsR0FBRyw0QkFBNEI7QUFDaEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkQ6XFxDYXBzdG9uZVByb2plY3RcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAZGF0ZS1mbnNcXHR6XFxkYXRlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUWkRhdGVNaW5pIH0gZnJvbSBcIi4vbWluaS5qc1wiO1xuXG4vKipcbiAqIFVUQyBkYXRlIGNsYXNzLiBJdCBtYXBzIGdldHRlcnMgYW5kIHNldHRlcnMgdG8gY29ycmVzcG9uZGluZyBVVEMgbWV0aG9kcyxcbiAqIGZvcmNpbmcgYWxsIGNhbGN1bGF0aW9ucyBpbiB0aGUgVVRDIHRpbWUgem9uZS5cbiAqXG4gKiBDb21iaW5lZCB3aXRoIGRhdGUtZm5zLCBpdCBhbGxvd3MgdXNpbmcgdGhlIGNsYXNzIHRoZSBzYW1lIHdheSBhc1xuICogdGhlIG9yaWdpbmFsIGRhdGUgY2xhc3MuXG4gKlxuICogVGhpcyBjb21wbGV0ZSB2ZXJzaW9uIHByb3ZpZGVzIG5vdCBvbmx5IGdldHRlcnMsIHNldHRlcnMsXG4gKiBhbmQgYGdldFRpbWV6b25lT2Zmc2V0YCwgYnV0IGFsc28gdGhlIGZvcm1hdHRlciBmdW5jdGlvbnMsIG1pcnJvcmluZ1xuICogYWxsIG9yaWdpbmFsIGBEYXRlYCBmdW5jdGlvbmFsaXR5LiBVc2UgdGhpcyB2ZXJzaW9uIHdoZW4geW91IG5lZWQgdG8gZm9ybWF0XG4gKiBhIHN0cmluZyBvciBpbiBhbiBlbnZpcm9ubWVudCB5b3UgZG9uJ3QgZnVsbHkgY29udHJvbCAoYSBsaWJyYXJ5KS5cbiAqIEZvciBhIG1pbmltYWwgdmVyc2lvbiwgc2VlIGBVVENEYXRlTWluaWAuXG4gKi9cbmV4cG9ydCBjbGFzcyBUWkRhdGUgZXh0ZW5kcyBUWkRhdGVNaW5pIHtcbiAgLy8jcmVnaW9uIHN0YXRpY1xuXG4gIHN0YXRpYyB0eih0eiwgLi4uYXJncykge1xuICAgIHJldHVybiBhcmdzLmxlbmd0aCA/IG5ldyBUWkRhdGUoLi4uYXJncywgdHopIDogbmV3IFRaRGF0ZShEYXRlLm5vdygpLCB0eik7XG4gIH1cblxuICAvLyNlbmRyZWdpb25cblxuICAvLyNyZWdpb24gcmVwcmVzZW50YXRpb25cblxuICB0b0lTT1N0cmluZygpIHtcbiAgICBjb25zdCBbc2lnbiwgaG91cnMsIG1pbnV0ZXNdID0gdGhpcy50ekNvbXBvbmVudHMoKTtcbiAgICBjb25zdCB0eiA9IGAke3NpZ259JHtob3Vyc306JHttaW51dGVzfWA7XG4gICAgcmV0dXJuIHRoaXMuaW50ZXJuYWwudG9JU09TdHJpbmcoKS5zbGljZSgwLCAtMSkgKyB0ejtcbiAgfVxuICB0b1N0cmluZygpIHtcbiAgICAvLyBcIlR1ZSBBdWcgMTMgMjAyNCAwNzo1MDoxOSBHTVQrMDgwMCAoU2luZ2Fwb3JlIFN0YW5kYXJkIFRpbWUpXCI7XG4gICAgcmV0dXJuIGAke3RoaXMudG9EYXRlU3RyaW5nKCl9ICR7dGhpcy50b1RpbWVTdHJpbmcoKX1gO1xuICB9XG4gIHRvRGF0ZVN0cmluZygpIHtcbiAgICAvLyB0b1VUQ1N0cmluZyByZXR1cm5zIFJGQyA3MjMxIChcIk1vbiwgMTIgQXVnIDIwMjQgMjM6MzY6MDggR01UXCIpXG4gICAgY29uc3QgW2RheSwgZGF0ZSwgbW9udGgsIHllYXJdID0gdGhpcy5pbnRlcm5hbC50b1VUQ1N0cmluZygpLnNwbGl0KFwiIFwiKTtcbiAgICAvLyBcIlR1ZSBBdWcgMTMgMjAyNFwiXG4gICAgcmV0dXJuIGAke2RheT8uc2xpY2UoMCwgLTEpIC8qIFJlbW92ZSBcIixcIiAqL30gJHttb250aH0gJHtkYXRlfSAke3llYXJ9YDtcbiAgfVxuICB0b1RpbWVTdHJpbmcoKSB7XG4gICAgLy8gdG9VVENTdHJpbmcgcmV0dXJucyBSRkMgNzIzMSAoXCJNb24sIDEyIEF1ZyAyMDI0IDIzOjM2OjA4IEdNVFwiKVxuICAgIGNvbnN0IHRpbWUgPSB0aGlzLmludGVybmFsLnRvVVRDU3RyaW5nKCkuc3BsaXQoXCIgXCIpWzRdO1xuICAgIGNvbnN0IFtzaWduLCBob3VycywgbWludXRlc10gPSB0aGlzLnR6Q29tcG9uZW50cygpO1xuICAgIC8vIFwiMDc6NDI6MjMgR01UKzA4MDAgKFNpbmdhcG9yZSBTdGFuZGFyZCBUaW1lKVwiXG4gICAgcmV0dXJuIGAke3RpbWV9IEdNVCR7c2lnbn0ke2hvdXJzfSR7bWludXRlc30gKCR7dHpOYW1lKHRoaXMudGltZVpvbmUsIHRoaXMpfSlgO1xuICB9XG4gIHRvTG9jYWxlU3RyaW5nKGxvY2FsZXMsIG9wdGlvbnMpIHtcbiAgICByZXR1cm4gRGF0ZS5wcm90b3R5cGUudG9Mb2NhbGVTdHJpbmcuY2FsbCh0aGlzLCBsb2NhbGVzLCB7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgdGltZVpvbmU6IG9wdGlvbnM/LnRpbWVab25lIHx8IHRoaXMudGltZVpvbmVcbiAgICB9KTtcbiAgfVxuICB0b0xvY2FsZURhdGVTdHJpbmcobG9jYWxlcywgb3B0aW9ucykge1xuICAgIHJldHVybiBEYXRlLnByb3RvdHlwZS50b0xvY2FsZURhdGVTdHJpbmcuY2FsbCh0aGlzLCBsb2NhbGVzLCB7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgdGltZVpvbmU6IG9wdGlvbnM/LnRpbWVab25lIHx8IHRoaXMudGltZVpvbmVcbiAgICB9KTtcbiAgfVxuICB0b0xvY2FsZVRpbWVTdHJpbmcobG9jYWxlcywgb3B0aW9ucykge1xuICAgIHJldHVybiBEYXRlLnByb3RvdHlwZS50b0xvY2FsZVRpbWVTdHJpbmcuY2FsbCh0aGlzLCBsb2NhbGVzLCB7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgdGltZVpvbmU6IG9wdGlvbnM/LnRpbWVab25lIHx8IHRoaXMudGltZVpvbmVcbiAgICB9KTtcbiAgfVxuXG4gIC8vI2VuZHJlZ2lvblxuXG4gIC8vI3JlZ2lvbiBwcml2YXRlXG5cbiAgdHpDb21wb25lbnRzKCkge1xuICAgIGNvbnN0IG9mZnNldCA9IHRoaXMuZ2V0VGltZXpvbmVPZmZzZXQoKTtcbiAgICBjb25zdCBzaWduID0gb2Zmc2V0ID4gMCA/IFwiLVwiIDogXCIrXCI7XG4gICAgY29uc3QgaG91cnMgPSBTdHJpbmcoTWF0aC5mbG9vcihNYXRoLmFicyhvZmZzZXQpIC8gNjApKS5wYWRTdGFydCgyLCBcIjBcIik7XG4gICAgY29uc3QgbWludXRlcyA9IFN0cmluZyhNYXRoLmFicyhvZmZzZXQpICUgNjApLnBhZFN0YXJ0KDIsIFwiMFwiKTtcbiAgICByZXR1cm4gW3NpZ24sIGhvdXJzLCBtaW51dGVzXTtcbiAgfVxuXG4gIC8vI2VuZHJlZ2lvblxuXG4gIHdpdGhUaW1lWm9uZSh0aW1lWm9uZSkge1xuICAgIHJldHVybiBuZXcgVFpEYXRlKCt0aGlzLCB0aW1lWm9uZSk7XG4gIH1cblxuICAvLyNyZWdpb24gZGF0ZS1mbnMgaW50ZWdyYXRpb25cblxuICBbU3ltYm9sLmZvcihcImNvbnN0cnVjdERhdGVGcm9tXCIpXShkYXRlKSB7XG4gICAgcmV0dXJuIG5ldyBUWkRhdGUoK25ldyBEYXRlKGRhdGUpLCB0aGlzLnRpbWVab25lKTtcbiAgfVxuXG4gIC8vI2VuZHJlZ2lvblxufVxuZnVuY3Rpb24gdHpOYW1lKHR6LCBkYXRlKSB7XG4gIHJldHVybiBuZXcgSW50bC5EYXRlVGltZUZvcm1hdChcImVuLUdCXCIsIHtcbiAgICB0aW1lWm9uZTogdHosXG4gICAgdGltZVpvbmVOYW1lOiBcImxvbmdcIlxuICB9KS5mb3JtYXQoZGF0ZSkuc2xpY2UoMTIpO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/date/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/date/mini.js":
/*!************************************************!*\
  !*** ./node_modules/@date-fns/tz/date/mini.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDateMini: () => (/* binding */ TZDateMini)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\");\n\nclass TZDateMini extends Date {\n  //#region static\n\n  constructor(...args) {\n    super();\n    if (args.length > 1 && typeof args[args.length - 1] === \"string\") {\n      this.timeZone = args.pop();\n    }\n    this.internal = new Date();\n    if (isNaN((0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this))) {\n      this.setTime(NaN);\n    } else {\n      if (!args.length) {\n        this.setTime(Date.now());\n      } else if (typeof args[0] === \"number\" && (args.length === 1 || args.length === 2 && typeof args[1] !== \"number\")) {\n        this.setTime(args[0]);\n      } else if (typeof args[0] === \"string\") {\n        this.setTime(+new Date(args[0]));\n      } else if (args[0] instanceof Date) {\n        this.setTime(+args[0]);\n      } else {\n        this.setTime(+new Date(...args));\n        adjustToSystemTZ(this, NaN);\n        syncToInternal(this);\n      }\n    }\n  }\n  static tz(tz, ...args) {\n    return args.length ? new TZDateMini(...args, tz) : new TZDateMini(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region time zone\n\n  withTimeZone(timeZone) {\n    return new TZDateMini(+this, timeZone);\n  }\n  getTimezoneOffset() {\n    return -(0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this);\n  }\n\n  //#endregion\n\n  //#region time\n\n  setTime(time) {\n    Date.prototype.setTime.apply(this, arguments);\n    syncToInternal(this);\n    return +this;\n  }\n\n  //#endregion\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDateMini(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\n\n// Assign getters and setters\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (!re.test(method)) return;\n  const utcMethod = method.replace(re, \"$1UTC\");\n  // Filter out methods without UTC counterparts\n  if (!TZDateMini.prototype[utcMethod]) return;\n  if (method.startsWith(\"get\")) {\n    // Delegate to internal date's UTC method\n    TZDateMini.prototype[method] = function () {\n      return this.internal[utcMethod]();\n    };\n  } else {\n    // Assign regular setter\n    TZDateMini.prototype[method] = function () {\n      Date.prototype[utcMethod].apply(this.internal, arguments);\n      syncFromInternal(this);\n      return +this;\n    };\n\n    // Assign UTC setter\n    TZDateMini.prototype[utcMethod] = function () {\n      Date.prototype[utcMethod].apply(this, arguments);\n      syncToInternal(this);\n      return +this;\n    };\n  }\n});\n\n/**\n * Function syncs time to internal date, applying the time zone offset.\n *\n * @param {Date} date - Date to sync\n */\nfunction syncToInternal(date) {\n  date.internal.setTime(+date);\n  date.internal.setUTCMinutes(date.internal.getUTCMinutes() - date.getTimezoneOffset());\n}\n\n/**\n * Function syncs the internal date UTC values to the date. It allows to get\n * accurate timestamp value.\n *\n * @param {Date} date - The date to sync\n */\nfunction syncFromInternal(date) {\n  // First we transpose the internal values\n  Date.prototype.setFullYear.call(date, date.internal.getUTCFullYear(), date.internal.getUTCMonth(), date.internal.getUTCDate());\n  Date.prototype.setHours.call(date, date.internal.getUTCHours(), date.internal.getUTCMinutes(), date.internal.getUTCSeconds(), date.internal.getUTCMilliseconds());\n\n  // Now we have to adjust the date to the system time zone\n  adjustToSystemTZ(date);\n}\n\n/**\n * Function adjusts the date to the system time zone. It uses the time zone\n * differences to calculate the offset and adjust the date.\n *\n * @param {Date} date - Date to adjust\n */\nfunction adjustToSystemTZ(date) {\n  // Save the time zone offset before all the adjustments\n  const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n\n  //#region System DST adjustment\n\n  // The biggest problem with using the system time zone is that when we create\n  // a date from internal values stored in UTC, the system time zone might end\n  // up on the DST hour:\n  //\n  //   $ TZ=America/New_York node\n  //   > new Date(2020, 2, 8, 1).toString()\n  //   'Sun Mar 08 2020 01:00:00 GMT-0500 (Eastern Standard Time)'\n  //   > new Date(2020, 2, 8, 2).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 3).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 4).toString()\n  //   'Sun Mar 08 2020 04:00:00 GMT-0400 (Eastern Daylight Time)'\n  //\n  // Here we get the same hour for both 2 and 3, because the system time zone\n  // has DST beginning at 8 March 2020, 2 a.m. and jumps to 3 a.m. So we have\n  // to adjust the internal date to reflect that.\n  //\n  // However we want to adjust only if that's the DST hour the change happenes,\n  // not the hour where DST moves to.\n\n  // We calculate the previous hour to see if the time zone offset has changed\n  // and we have landed on the DST hour.\n  const prevHour = new Date(+date);\n  // We use UTC methods here as we don't want to land on the same hour again\n  // in case of DST.\n  prevHour.setUTCHours(prevHour.getUTCHours() - 1);\n\n  // Calculate if we are on the system DST hour.\n  const systemOffset = -new Date(+date).getTimezoneOffset();\n  const prevHourSystemOffset = -new Date(+prevHour).getTimezoneOffset();\n  const systemDSTChange = systemOffset - prevHourSystemOffset;\n  // Detect the DST shift. System DST change will occur both on\n  const dstShift = Date.prototype.getHours.apply(date) !== date.internal.getUTCHours();\n\n  // Move the internal date when we are on the system DST hour.\n  if (systemDSTChange && dstShift) date.internal.setUTCMinutes(date.internal.getUTCMinutes() + systemDSTChange);\n\n  //#endregion\n\n  //#region System diff adjustment\n\n  // Now we need to adjust the date, since we just applied internal values.\n  // We need to calculate the difference between the system and date time zones\n  // and apply it to the date.\n\n  const offsetDiff = systemOffset - offset;\n  if (offsetDiff) Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetDiff);\n\n  //#endregion\n\n  //#region Post-adjustment DST fix\n\n  const postOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n  const postSystemOffset = -new Date(+date).getTimezoneOffset();\n  const postOffsetDiff = postSystemOffset - postOffset;\n  const offsetChanged = postOffset !== offset;\n  const postDiff = postOffsetDiff - offsetDiff;\n  if (offsetChanged && postDiff) {\n    Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + postDiff);\n\n    // Now we need to check if got offset change during the post-adjustment.\n    // If so, we also need both dates to reflect that.\n\n    const newOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n    const offsetChange = postOffset - newOffset;\n    if (offsetChange) {\n      date.internal.setUTCMinutes(date.internal.getUTCMinutes() + offsetChange);\n      Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetChange);\n    }\n  }\n\n  //#endregion\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/date/mini.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/index.js":
/*!********************************************!*\
  !*** ./node_modules/@date-fns/tz/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDate: () => (/* reexport safe */ _date_index_js__WEBPACK_IMPORTED_MODULE_1__.TZDate),\n/* harmony export */   TZDateMini: () => (/* reexport safe */ _date_mini_js__WEBPACK_IMPORTED_MODULE_2__.TZDateMini),\n/* harmony export */   constructFromSymbol: () => (/* reexport safe */ _constants_index_js__WEBPACK_IMPORTED_MODULE_0__.constructFromSymbol),\n/* harmony export */   tz: () => (/* reexport safe */ _tz_index_js__WEBPACK_IMPORTED_MODULE_3__.tz),\n/* harmony export */   tzOffset: () => (/* reexport safe */ _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_4__.tzOffset),\n/* harmony export */   tzScan: () => (/* reexport safe */ _tzScan_index_js__WEBPACK_IMPORTED_MODULE_5__.tzScan)\n/* harmony export */ });\n/* harmony import */ var _constants_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants/index.js */ \"(ssr)/./node_modules/@date-fns/tz/constants/index.js\");\n/* harmony import */ var _date_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./date/index.js */ \"(ssr)/./node_modules/@date-fns/tz/date/index.js\");\n/* harmony import */ var _date_mini_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./date/mini.js */ \"(ssr)/./node_modules/@date-fns/tz/date/mini.js\");\n/* harmony import */ var _tz_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tz/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tz/index.js\");\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tzOffset/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\");\n/* harmony import */ var _tzScan_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tzScan/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzScan/index.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUNMO0FBQ0Q7QUFDRDtBQUNNIiwic291cmNlcyI6WyJEOlxcQ2Fwc3RvbmVQcm9qZWN0XFxPcGVuQXV0b21hdGUuRnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcQGRhdGUtZm5zXFx0elxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY29uc3RhbnRzL2luZGV4LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9kYXRlL2luZGV4LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9kYXRlL21pbmkuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R6L2luZGV4LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90ek9mZnNldC9pbmRleC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHpTY2FuL2luZGV4LmpzXCI7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tz/index.js":
/*!***********************************************!*\
  !*** ./node_modules/@date-fns/tz/tz/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tz: () => (/* binding */ tz)\n/* harmony export */ });\n/* harmony import */ var _date_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../date/index.js */ \"(ssr)/./node_modules/@date-fns/tz/date/index.js\");\n\n\n/**\n * The function creates accepts a time zone and returns a function that creates\n * a new `TZDate` instance in the time zone from the provided value. Use it to\n * provide the context for the date-fns functions, via the `in` option.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n *\n * @returns Function that creates a new `TZDate` instance in the time zone\n */\nconst tz = timeZone => value => _date_index_js__WEBPACK_IMPORTED_MODULE_0__.TZDate.tz(timeZone, +new Date(value));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L3R6L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBDOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyxnQ0FBZ0Msa0RBQU0iLCJzb3VyY2VzIjpbIkQ6XFxDYXBzdG9uZVByb2plY3RcXE9wZW5BdXRvbWF0ZS5Gcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAZGF0ZS1mbnNcXHR6XFx0elxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVFpEYXRlIH0gZnJvbSBcIi4uL2RhdGUvaW5kZXguanNcIjtcblxuLyoqXG4gKiBUaGUgZnVuY3Rpb24gY3JlYXRlcyBhY2NlcHRzIGEgdGltZSB6b25lIGFuZCByZXR1cm5zIGEgZnVuY3Rpb24gdGhhdCBjcmVhdGVzXG4gKiBhIG5ldyBgVFpEYXRlYCBpbnN0YW5jZSBpbiB0aGUgdGltZSB6b25lIGZyb20gdGhlIHByb3ZpZGVkIHZhbHVlLiBVc2UgaXQgdG9cbiAqIHByb3ZpZGUgdGhlIGNvbnRleHQgZm9yIHRoZSBkYXRlLWZucyBmdW5jdGlvbnMsIHZpYSB0aGUgYGluYCBvcHRpb24uXG4gKlxuICogQHBhcmFtIHRpbWVab25lIC0gVGltZSB6b25lIG5hbWUgKElBTkEgb3IgVVRDIG9mZnNldClcbiAqXG4gKiBAcmV0dXJucyBGdW5jdGlvbiB0aGF0IGNyZWF0ZXMgYSBuZXcgYFRaRGF0ZWAgaW5zdGFuY2UgaW4gdGhlIHRpbWUgem9uZVxuICovXG5leHBvcnQgY29uc3QgdHogPSB0aW1lWm9uZSA9PiB2YWx1ZSA9PiBUWkRhdGUudHoodGltZVpvbmUsICtuZXcgRGF0ZSh2YWx1ZSkpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tz/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/@date-fns/tz/tzOffset/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzOffset: () => (/* binding */ tzOffset)\n/* harmony export */ });\nconst offsetFormatCache = {};\nconst offsetCache = {};\n\n/**\n * The function extracts UTC offset in minutes from the given date in specified\n * time zone.\n *\n * Unlike `Date.prototype.getTimezoneOffset`, this function returns the value\n * mirrored to the sign of the offset in the time zone. For Asia/Singapore\n * (UTC+8), `tzOffset` returns 480, while `getTimezoneOffset` returns -480.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date to check the offset for\n *\n * @returns UTC offset in minutes\n */\nfunction tzOffset(timeZone, date) {\n  try {\n    const format = offsetFormatCache[timeZone] ||= new Intl.DateTimeFormat(\"en-GB\", {\n      timeZone,\n      hour: \"numeric\",\n      timeZoneName: \"longOffset\"\n    }).format;\n    const offsetStr = format(date).split('GMT')[1] || '';\n    if (offsetStr in offsetCache) return offsetCache[offsetStr];\n    return calcOffset(offsetStr, offsetStr.split(\":\"));\n  } catch {\n    // Fallback to manual parsing if the runtime doesn't support ±HH:MM/±HHMM/±HH\n    // See: https://github.com/nodejs/node/issues/53419\n    if (timeZone in offsetCache) return offsetCache[timeZone];\n    const captures = timeZone?.match(offsetRe);\n    if (captures) return calcOffset(timeZone, captures.slice(1));\n    return NaN;\n  }\n}\nconst offsetRe = /([+-]\\d\\d):?(\\d\\d)?/;\nfunction calcOffset(cacheStr, values) {\n  const hours = +values[0];\n  const minutes = +(values[1] || 0);\n  return offsetCache[cacheStr] = hours > 0 ? hours * 60 + minutes : hours * 60 - minutes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@date-fns/tz/tzScan/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@date-fns/tz/tzScan/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzScan: () => (/* binding */ tzScan)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/./node_modules/@date-fns/tz/tzOffset/index.js\");\n\n\n/**\n * Time interval.\n */\n\n/**\n * Time zone change record.\n */\n\n/**\n * The function scans the time zone for changes in the given interval.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param interval - Time interval to scan for changes\n *\n * @returns Array of time zone changes\n */\nfunction tzScan(timeZone, interval) {\n  const changes = [];\n  const monthDate = new Date(interval.start);\n  monthDate.setUTCSeconds(0, 0);\n  const endDate = new Date(interval.end);\n  endDate.setUTCSeconds(0, 0);\n  const endMonthTime = +endDate;\n  let lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, monthDate);\n  while (+monthDate < endMonthTime) {\n    // Month forward\n    monthDate.setUTCMonth(monthDate.getUTCMonth() + 1);\n\n    // Find the month where the offset changes\n    const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, monthDate);\n    if (offset != lastOffset) {\n      // Rewind a month back to find the day where the offset changes\n      const dayDate = new Date(monthDate);\n      dayDate.setUTCMonth(dayDate.getUTCMonth() - 1);\n      const endDayTime = +monthDate;\n      lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, dayDate);\n      while (+dayDate < endDayTime) {\n        // Day forward\n        dayDate.setUTCDate(dayDate.getUTCDate() + 1);\n\n        // Find the day where the offset changes\n        const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, dayDate);\n        if (offset != lastOffset) {\n          // Rewind a day back to find the time where the offset changes\n          const hourDate = new Date(dayDate);\n          hourDate.setUTCDate(hourDate.getUTCDate() - 1);\n          const endHourTime = +dayDate;\n          lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, hourDate);\n          while (+hourDate < endHourTime) {\n            // Hour forward\n            hourDate.setUTCHours(hourDate.getUTCHours() + 1);\n\n            // Find the hour where the offset changes\n            const hourOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, hourDate);\n            if (hourOffset !== lastOffset) {\n              changes.push({\n                date: new Date(hourDate),\n                change: hourOffset - lastOffset,\n                offset: hourOffset\n              });\n            }\n            lastOffset = hourOffset;\n          }\n        }\n        lastOffset = offset;\n      }\n    }\n    lastOffset = offset;\n  }\n  return changes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@date-fns/tz/tzScan/index.js\n");

/***/ })

};
;